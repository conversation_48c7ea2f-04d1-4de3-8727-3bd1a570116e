"""
Lightweight Metrics Module
Provides simple event logging and performance tracking without external dependencies
"""

import json
import time
import functools
from pathlib import Path
from typing import Dict, Any, Callable, Union
from datetime import datetime, timezone


# Global metrics file path
METRICS_FILE = Path("logs/metrics.ndjson")


def log_event(event: str, data: Dict[str, Any] | None = None) -> None:
    """
    Log an event with optional data to metrics file
    
    Args:
        event: Event name/type
        data: Optional event data dictionary
    """
    # Ensure logs directory exists
    METRICS_FILE.parent.mkdir(parents=True, exist_ok=True)
    
    # Create metric entry
    metric = {
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "event": event,
        "data": data or {}
    }
    
    # Append to NDJSON file
    with open(METRICS_FILE, "a", encoding="utf-8") as f:
        f.write(json.dumps(metric, ensure_ascii=False) + "\n")


def histogram(name: str, value: float) -> None:
    """
    Record a histogram value
    
    Args:
        name: Histogram name
        value: Numeric value to record
    """
    log_event("histogram", {
        "name": name,
        "value": value
    })


def flush() -> None:
    """
    Flush metrics to disk/stdout
    Currently a no-op since we write immediately, but provided for API compatibility
    """
    pass


def track_latency(event_name: str):
    """
    Decorator to track function execution latency in milliseconds
    Works with both sync and async functions
    
    Args:
        event_name: Name for the latency event
    """
    def decorator(func: Callable) -> Callable:
        if hasattr(func, '__call__') and hasattr(func, '__await__'):
            # Async function
            @functools.wraps(func)
            async def async_wrapper(*args, **kwargs):
                start_time = time.perf_counter()
                try:
                    result = await func(*args, **kwargs)
                    return result
                finally:
                    end_time = time.perf_counter()
                    latency_ms = (end_time - start_time) * 1000
                    log_event("latency", {
                        "event_name": event_name,
                        "latency_ms": latency_ms,
                        "function": func.__name__
                    })
            return async_wrapper
        else:
            # Sync function
            @functools.wraps(func)
            def sync_wrapper(*args, **kwargs):
                start_time = time.perf_counter()
                try:
                    result = func(*args, **kwargs)
                    return result
                finally:
                    end_time = time.perf_counter()
                    latency_ms = (end_time - start_time) * 1000
                    log_event("latency", {
                        "event_name": event_name,
                        "latency_ms": latency_ms,
                        "function": func.__name__
                    })
            return sync_wrapper
    return decorator


def count_calls(event_name: str):
    """
    Decorator to count function calls
    
    Args:
        event_name: Name for the call count event
    """
    def decorator(func: Callable) -> Callable:
        if hasattr(func, '__call__') and hasattr(func, '__await__'):
            # Async function
            @functools.wraps(func)
            async def async_wrapper(*args, **kwargs):
                log_event("call_count", {
                    "event_name": event_name,
                    "function": func.__name__
                })
                return await func(*args, **kwargs)
            return async_wrapper
        else:
            # Sync function
            @functools.wraps(func)
            def sync_wrapper(*args, **kwargs):
                log_event("call_count", {
                    "event_name": event_name,
                    "function": func.__name__
                })
                return func(*args, **kwargs)
            return sync_wrapper
    return decorator


# Utility functions for reading metrics
def read_metrics() -> list[dict]:
    """
    Read all metrics from the metrics file
    
    Returns:
        List of metric dictionaries
    """
    if not METRICS_FILE.exists():
        return []
    
    metrics = []
    with open(METRICS_FILE, "r", encoding="utf-8") as f:
        for line in f:
            line = line.strip()
            if line:
                try:
                    metrics.append(json.loads(line))
                except json.JSONDecodeError:
                    continue
    
    return metrics


def clear_metrics() -> None:
    """
    Clear all metrics by removing the metrics file
    """
    if METRICS_FILE.exists():
        METRICS_FILE.unlink()
