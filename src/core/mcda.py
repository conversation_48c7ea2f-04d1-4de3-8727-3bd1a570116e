"""
MCDA (Multi-Criteria Decision Analysis) Module - Vectorized Version
High-performance rule-based scoring system using numpy operations
"""

import yaml
import numpy as np
from pathlib import Path
from typing import List, Dict, Any, Tuple, Optional

# Cache for weights to avoid repeated YAML loading
_weights_cache = None


def load_weights() -> Dict[str, Any]:
    """
    Load MCDA weights from YAML configuration (cached)

    Returns:
        Dict[str, Any]: Complete weights configuration
    """
    global _weights_cache

    if _weights_cache is not None:
        return _weights_cache

    weights_file = Path(__file__).parent / "weights.yaml"

    try:
        with open(weights_file, 'r', encoding='utf-8') as f:
            weights = yaml.safe_load(f)
        _weights_cache = weights
        return weights
    except (FileNotFoundError, yaml.YAMLError) as e:
        print(f"Warning: Could not load weights.yaml ({e}), using defaults")
        # Return default weights if file not found or invalid
        default_weights = {
            'question_weights': {
                'location_preference': 4.5,
                'budget_range': 5.0,
                'field_of_interest': 4.8,
                'career_goal': 4.5,
            },
            'field_weights': {'STEM': 1.0, 'Business': 1.0, 'Other': 0.8},
            'location_weights': {'ភ្នំពេញ': 1.0, 'other': 0.7},
            'budget_weights': {'Low': 1.0, 'Medium': 1.0, 'High': 1.0},
            'scholarship_bonus': 0.3,
            'employment_bonus_multiplier': 0.2
        }
        _weights_cache = default_weights
        return default_weights


def score_vectorized(user_answers: Dict[str, Any], programmes: Optional[List[Dict[str, Any]]]) -> List[float]:
    """
    Calculate MCDA scores for multiple programs using vectorized operations

    Args:
        user_answers: User's answers to assessment questions
        programmes: List of program data dictionaries

    Returns:
        List[float]: List of MCDA scores (0.0 to 1.0) for each program
    """
    if not programmes:
        return []

    try:
        weights = load_weights()
        # Keep only weights that the user actually answered
        question_weights = {k: v for k, v in weights.get('question_weights', {}).items() if k in user_answers}
        weights['question_weights'] = question_weights
    except Exception:
        # Fallback to minimal weights if loading fails
        weights = {
            'question_weights': {},
            'field_weights': {},
            'location_weights': {},
            'budget_weights': {},
            'scholarship_bonus': 0.0,
            'employment_bonus_multiplier': 0.0
        }

    n_programs = len(programmes)
    scores = np.zeros(n_programs)

    # Extract program data into arrays for vectorized operations
    cities = np.array([prog.get('city', '') for prog in programmes])
    tuition_fees = np.array([_safe_float(prog.get('tuition_fees_usd', 0)) for prog in programmes])
    employment_rates = np.array([_safe_float(prog.get('employment_rate', 0)) for prog in programmes])
    scholarship_flags = np.array([prog.get('scholarship_availability', False) for prog in programmes])

    # Field matching arrays
    is_stem = np.array([prog.get('is_stem', False) for prog in programmes])
    is_business = np.array([prog.get('is_business', False) for prog in programmes])
    is_arts = np.array([prog.get('is_arts', False) for prog in programmes])

    # Location matching arrays
    is_phnom_penh = np.array([prog.get('is_phnom_penh', False) for prog in programmes])
    is_siem_reap = np.array([prog.get('is_siem_reap', False) for prog in programmes])
    is_battambang = np.array([prog.get('is_battambang', False) for prog in programmes])

    # Budget matching arrays
    is_low_cost = np.array([prog.get('is_low_cost', False) for prog in programmes])
    is_medium_cost = np.array([prog.get('is_medium_cost', False) for prog in programmes])
    is_high_cost = np.array([prog.get('is_high_cost', False) for prog in programmes])

    # Calculate component scores
    location_scores = _calculate_location_scores_vectorized(
        user_answers, weights, is_phnom_penh, is_siem_reap, is_battambang
    )

    budget_scores = _calculate_budget_scores_vectorized(
        user_answers, weights, is_low_cost, is_medium_cost, is_high_cost
    )

    field_scores = _calculate_field_scores_vectorized(
        user_answers, weights, is_stem, is_business, is_arts
    )

    # Apply bonuses
    scholarship_bonus = weights.get('scholarship_bonus', 0.0)
    employment_multiplier = weights.get('employment_bonus_multiplier', 0.0)

    # Scholarship bonus (vectorized)
    scholarship_bonuses = np.where(
        scholarship_flags & (user_answers.get('scholarship_need') == 'yes'),
        scholarship_bonus,
        0.0
    )

    # Employment rate bonus (vectorized)
    employment_bonuses = employment_rates * employment_multiplier / 100.0

    # Combine all scores
    base_scores = location_scores + budget_scores + field_scores
    total_scores = base_scores + scholarship_bonuses + employment_bonuses

    # Normalize to 0-1 range
    if len(weights.get('question_weights', {})) > 0:
        max_possible = sum(weights['question_weights'].values()) + scholarship_bonus + employment_multiplier
        normalized_scores = np.clip(total_scores / max_possible, 0.0, 1.0)
    else:
        # Fallback normalization
        normalized_scores = np.clip(total_scores / 10.0, 0.0, 1.0)

    # Always give a plain-Python list so callers can json-serialise easily
    return normalized_scores.tolist()


def score(user_answers: Dict[str, Any], programme: Optional[Dict[str, Any]]) -> float:
    """
    Calculate MCDA score for a single program (legacy compatibility)

    Args:
        user_answers: User's answers to assessment questions
        programme: Program data dictionary

    Returns:
        float: MCDA score (0.0 to 1.0)
    """
    if programme is None or user_answers is None:
        return 0.0

    scores = score_vectorized(user_answers, [programme])
    return float(scores[0]) if len(scores) > 0 else 0.0


def _safe_float(value: Any) -> float:
    """Safely convert value to float"""
    try:
        if isinstance(value, str):
            cleaned = value.replace(',', '').replace('$', '').replace('%', '').strip()
            return float(cleaned) if cleaned else 0.0
        return float(value) if value is not None else 0.0
    except (ValueError, TypeError):
        return 0.0


def _calculate_location_scores_vectorized(
    user_answers: Dict[str, Any],
    weights: Dict[str, Any],
    is_phnom_penh: np.ndarray,
    is_siem_reap: np.ndarray,
    is_battambang: np.ndarray
) -> np.ndarray:
    """Calculate location matching scores using vectorized operations"""
    location_weight = weights.get('question_weights', {}).get('location_preference', 0.0)
    if location_weight == 0:
        return np.zeros(len(is_phnom_penh))

    user_location = user_answers.get('location_preference', '')

    # Map user preference to location flags
    location_mapping = {
        'pp': is_phnom_penh,
        'sr': is_siem_reap,
        'btb': is_battambang,
        'phnom_penh': is_phnom_penh,
        'siem_reap': is_siem_reap,
        'battambang': is_battambang
    }

    if user_location in location_mapping:
        # Perfect match for preferred location
        preferred_match = location_mapping[user_location]
        # Partial match for other major cities
        other_major_cities = is_phnom_penh | is_siem_reap | is_battambang

        scores = np.where(
            preferred_match,
            location_weight,  # Perfect match
            np.where(
                other_major_cities,
                location_weight * 0.7,  # Partial match for major cities
                location_weight * 0.3   # Small score for other locations
            )
        )
    else:
        # Default scoring if preference not recognized
        scores = np.full(len(is_phnom_penh), location_weight * 0.5)

    return scores


def _calculate_budget_scores_vectorized(
    user_answers: Dict[str, Any],
    weights: Dict[str, Any],
    is_low_cost: np.ndarray,
    is_medium_cost: np.ndarray,
    is_high_cost: np.ndarray
) -> np.ndarray:
    """Calculate budget matching scores using vectorized operations"""
    budget_weight = weights.get('question_weights', {}).get('budget_range', 0.0)
    if budget_weight == 0:
        return np.zeros(len(is_low_cost))

    user_budget = user_answers.get('budget_range', '')

    if user_budget == 'low':
        scores = np.where(is_low_cost, budget_weight, budget_weight * 0.3)
    elif user_budget == 'medium':
        scores = np.where(is_medium_cost, budget_weight, budget_weight * 0.3)
    elif user_budget == 'high':
        scores = np.where(is_high_cost, budget_weight, budget_weight * 0.3)
    else:
        # Default scoring
        scores = np.full(len(is_low_cost), budget_weight * 0.5)

    return scores


def _calculate_field_scores_vectorized(
    user_answers: Dict[str, Any],
    weights: Dict[str, Any],
    is_stem: np.ndarray,
    is_business: np.ndarray,
    is_arts: np.ndarray
) -> np.ndarray:
    """Calculate field matching scores using vectorized operations"""
    field_weight = weights.get('question_weights', {}).get('field_of_interest', 0.0)
    if field_weight == 0:
        return np.zeros(len(is_stem))

    user_field = user_answers.get('field_of_interest', '')

    if user_field == 'stem':
        scores = np.where(is_stem, field_weight, field_weight * 0.2)
    elif user_field == 'business':
        scores = np.where(is_business, field_weight, field_weight * 0.2)
    elif user_field == 'arts':
        scores = np.where(is_arts, field_weight, field_weight * 0.2)
    else:
        # Default scoring for other fields
        scores = np.full(len(is_stem), field_weight * 0.5)

    return scores


def recommender_mcda(user_answers: Dict[str, Any], programmes: List[Dict[str, Any]],
                    top_n: int = 5) -> List[Tuple[Dict[str, Any], float, str]]:
    """
    Generate top N recommendations using vectorized MCDA scoring

    Args:
        user_answers: User's assessment answers
        programmes: List of all available programs
        top_n: Number of top recommendations to return

    Returns:
        List[Tuple]: List of (program, score, reason) tuples
    """
    if not programmes:
        return []

    # Use vectorized scoring for better performance
    scores = score_vectorized(user_answers, programmes)

    # Create scored programs list
    scored_programs = []
    for i, programme in enumerate(programmes):
        program_score = float(scores[i])
        reason = generate_reason(user_answers, programme, program_score)
        scored_programs.append((programme, program_score, reason))

    # Sort by score descending
    scored_programs.sort(key=lambda x: x[1], reverse=True)

    # Return top N
    return scored_programs[:top_n]


def generate_reason(user_answers: Dict[str, Any], programme: Dict[str, Any],
                   score: float) -> str:
    """
    Generate explanation for why a program was recommended

    Args:
        user_answers: User's assessment answers
        programme: Program data
        score: MCDA score

    Returns:
        str: Human-readable explanation in Khmer
    """
    reasons = []

    # Location match
    if 'location_preference' in user_answers:
        user_location = user_answers['location_preference']

        if user_location in ['pp', 'phnom_penh'] and programme.get('is_phnom_penh'):
            reasons.append("ស្ថិតនៅភ្នំពេញតាមការចង់បាន")
        elif user_location in ['sr', 'siem_reap'] and programme.get('is_siem_reap'):
            reasons.append("ស្ថិតនៅសៀមរាបតាមការចង់បាន")
        elif user_location in ['btb', 'battambang'] and programme.get('is_battambang'):
            reasons.append("ស្ថិតនៅបាត់ដំបងតាមការចង់បាន")

    # Budget match
    if 'budget_range' in user_answers:
        user_budget = user_answers['budget_range']

        if user_budget == 'low' and programme.get('is_low_cost'):
            reasons.append("តម្លៃសិក្សាសមរម្យសម្រាប់ថវិកាទាប")
        elif user_budget == 'medium' and programme.get('is_medium_cost'):
            reasons.append("តម្លៃសិក្សាស្របតាមថវិកាមធ្យម")
        elif user_budget == 'high' and programme.get('is_high_cost'):
            reasons.append("តម្លៃសិក្សាសម្រាប់ថវិកាខ្ពស់")

    # Field match
    if 'field_of_interest' in user_answers:
        user_field = user_answers['field_of_interest']

        if user_field == 'stem' and programme.get('is_stem'):
            reasons.append("ជំនាញវិទ្យាសាស្ត្រនិងបច្ចេកវិទ្យាតាមចំណាប់អារម្មណ៍")
        elif user_field == 'business' and programme.get('is_business'):
            reasons.append("ជំនាញអាជីវកម្មតាមចំណាប់អារម្មណ៍")
        elif user_field == 'arts' and programme.get('is_arts'):
            reasons.append("ជំនាញសិល្បៈតាមចំណាប់អារម្មណ៍")

    # Scholarship availability
    if user_answers.get('scholarship_need') == 'yes' and programme.get('scholarship_availability'):
        reasons.append("មានអាហារូបករណ៍")

    # Employment rate
    employment_rate = programme.get('employment_rate', 0)
    if employment_rate and employment_rate >= 80:
        reasons.append(f"អត្រាការងារខ្ពស់ {employment_rate}%")

    # Combine reasons
    if reasons:
        return "ផ្តល់អនុសាសន៍ដោយសារ: " + ", ".join(reasons)
    else:
        return f"ពិន្ទុផ្គូផ្គង {score:.2f}"
