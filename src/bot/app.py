"""
EduGuideBot Main Application
Entry point for the Telegram bot
"""

import os
import sys
import logging
import argparse
from pathlib import Path
from telegram.ext import Application, CommandHandler, MessageHandler, CallbackQueryHandler
from dotenv import load_dotenv

# Add project root to path for imports
if __name__ == '__main__':
    sys.path.append(str(Path(__file__).parents[2]))

# Add project root to path for script execution
if __name__ == '__main__':
    sys.path.append(str(Path(__file__).parents[2]))
    from src.bot.commands import (start_command, recommend_command, help_command, cancel_command,
                                  filter_language_command, campus_map_command, study_plan_command,
                                  mental_health_command, settings_command, handle_language_setting, details_command)
    from src.bot.handlers import create_conversation_handler
else:
    from .commands import (start_command, recommend_command, help_command, cancel_command,
                          filter_language_command, campus_map_command, study_plan_command,
                          mental_health_command, settings_command, handle_language_setting, details_command)
    from .handlers import create_conversation_handler

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# Global flag for handler registration
_handlers_registered = False


def main(dry_run: bool = False, test_lang: str = None) -> None:
    """
    Main function to run the Telegram bot

    Args:
        dry_run: If True, skip network calls and exit cleanly
        test_lang: Language to test in dry-run mode
    """
    # Get bot token from environment
    bot_token = os.getenv('BOT_TOKEN')

    # Handle dry-run mode
    if dry_run or not bot_token or bot_token in ["test_token", "dry"]:
        print("🛈 Bot initialised (dry-run mode) – no network calls")

        # Test i18n functionality if language specified
        if test_lang:
            if __name__ == '__main__':
                from src.bot.i18n import t
            else:
                from .i18n import t
            greeting = t("start_greeting", test_lang)
            print(f"🌐 Testing {test_lang}: {greeting}")

        return

    # Create Application instance
    application = Application.builder().token(bot_token).build()

    # Add command handlers (idempotent registration)
    _add_command_handlers(application)

    # Add error handler
    if __name__ == '__main__':
        from src.bot.error_handler import error_handler
    else:
        from .error_handler import error_handler
    application.add_error_handler(error_handler)

    # Start bot polling
    logger.info("Starting EduGuideBot...")
    application.run_polling(allowed_updates=["message", "callback_query"])


def _add_command_handlers(application) -> None:
    """
    Add command handlers to application (idempotent)

    Args:
        application: Telegram Application instance
    """
    global _handlers_registered
    if _handlers_registered:
        logger.debug("Handlers already registered – skipping")
        return

    from telegram.ext import MessageHandler, filters

    # Basic command handlers
    application.add_handler(CommandHandler("start", start_command))
    application.add_handler(CommandHandler("help", help_command))
    application.add_handler(CommandHandler("cancel", cancel_command))
    application.add_handler(CommandHandler("filterlanguage", filter_language_command))
    application.add_handler(CommandHandler("campusmap", campus_map_command))
    application.add_handler(CommandHandler("studyplan", study_plan_command))
    application.add_handler(CommandHandler("mentalhealth", mental_health_command))
    application.add_handler(CommandHandler("settings", settings_command))

    # Details command handler (pattern-based for dynamic IDs)
    application.add_handler(MessageHandler(filters.Regex(r'^/details_'), details_command))

    # Conversation handler
    conversation_handler = create_conversation_handler()
    application.add_handler(conversation_handler)

    # Callback handlers
    application.add_handler(CallbackQueryHandler(handle_language_setting, pattern=r'^lang_'))

    # Import and register inline button callbacks
    from .handlers import back_to_list_callback, explain_rec_callback
    application.add_handler(CallbackQueryHandler(back_to_list_callback, pattern=r'^back_to_list$'))
    application.add_handler(CallbackQueryHandler(explain_rec_callback, pattern=r'^explain_rec:'))

    # Import and register filters callback
    from .commands import handle_filters_callback
    application.add_handler(CallbackQueryHandler(handle_filters_callback, pattern=r'^(show_filters|toggle_low_cost|toggle_phnom_penh|back_to_settings)$'))

    _handlers_registered = True


if __name__ == '__main__':
    """
    Entry point when running as script
    """
    parser = argparse.ArgumentParser(description='EduGuideBot Telegram Application')
    parser.add_argument('--dry-run', action='store_true',
                       help='Run in dry-run mode (no network calls)')
    parser.add_argument('--lang', choices=['kh', 'en'],
                       help='Test language in dry-run mode')
    args = parser.parse_args()

    try:
        main(dry_run=args.dry_run, test_lang=args.lang)
    except KeyboardInterrupt:
        logger.info("Bot stopped by user")
    except Exception as e:
        logger.error(f"Bot crashed: {e}")
        raise


# Export symbols for testing
__all__ = [
    "_add_command_handlers",
    "create_conversation_handler",
    "MessageHandler",
    "error_handler",
]
