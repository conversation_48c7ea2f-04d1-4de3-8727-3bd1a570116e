{"start_greeting": "Hello! 👋 Welcome to EduGuideBot", "start_description": "🎓 I will help you find the most suitable study programs in Cambodia", "start_button": "Start Assessment", "assessment_title": "🎓 **EduGuideBot Skills Assessment**", "assessment_progress": "📊 Assessment: {current}/{total}", "assessment_complete": "✅ **Assessment Complete!**", "recommendations_title": "🎯 **Study Program Recommendations for You:**", "recommendation_card_details": "🔍 More Details /details_{id}", "restart_message": "🔄 Want to take the assessment again? /start", "error_recommendation": "❌ Error generating recommendations: {error}", "details_coming_soon": "🔍 Detailed information will be available in the next version. Thank you!", "cancel_message": "❌ Assessment cancelled. Want to start again? /start", "generic_error": "Sorry – something went wrong. Please try again.", "command_error": "Sorry – there was an error processing the {command} command.", "filter_language_usage": "❌ Please specify language: /filterlanguage kh, /filterlanguage en, or /filterlanguage both", "filter_language_invalid": "❌ Invalid language. Please use: kh, en, or both", "help_title": "📚 **EduGuideBot Help**", "help_description": "EduGuideBot is an AI-powered university recommendation system", "help_commands": "**Command List:**\n/start - Start assessment\n/help - Show help\n/settings - Language settings\n/cancel - Cancel assessment", "help_features": "**Features:**\n• 16-question assessment\n• Hybrid scoring system (70% MCDA + 30% ML)\n• 47 universities information\n• 500+ study programs", "help_contact": "**Contact:** For additional help, please contact the development team", "hybrid_score_explanation": "📊 **Hybrid Score = 70% MCDA + 30% ML**", "mcda_explanation": "MCDA (Multi-Criteria Decision Analysis) evaluates programs based on your preferences", "ml_explanation": "ML (Machine Learning) component learns from successful student outcomes", "score_breakdown": "📈 Score Breakdown:\n• MCDA: {mcda_score:.2f}\n• ML: {ml_score:.2f}\n• Hybrid: {hybrid_score:.2f}", "why_recommended": "🤔 **Why this recommendation?**\n{reason}", "button_back_to_list": "🔙 Back to list", "button_why_recommended": "📊 Why this rec?", "button_filters": "🔧 Filters", "button_low_cost_only": "💰 Low-cost only", "button_phnom_penh_only": "🏛️ Phnom Penh only", "settings_filters": "🔧 **Filters**", "filter_low_cost": "💰 Show only low-cost programs (<$500)", "filter_phnom_penh": "🏛️ Show only Phnom Penh programs", "filter_enabled": "✅ Enabled", "filter_disabled": "❌ Disabled", "filters_updated": "✅ Filters updated successfully", "settings_title": "⚙️ **Settings**", "settings_language": "🌐 **Language:** {language}", "settings_change_language": "Change Language", "language_khmer": "🇰🇭 Khmer", "language_english": "🇬🇧 English", "language_changed": "✅ Language changed to {language}", "question_location": "🏛️ Which city/province would you like to study in?", "question_budget": "💰 What is your annual study budget?", "question_field": "🔬 Which field interests you?", "question_career": "🎯 Future career goals?", "question_academic": "📚 Which subject are you best at?", "question_learning": "📝 What learning style do you prefer?", "question_study_mode": "⏰ What study schedule suits you?", "question_language": "🗣️ Language of instruction?", "question_scholarship": "🎓 Do you need a scholarship?", "question_campus": "🏫 How important is campus life?", "question_extracurricular": "🎨 Which extracurricular activities interest you?", "question_employment": "💼 What's most important for your career?", "question_mental_health": "🧘‍♂️ Is mental health support important to you?", "question_international": "🌏 Do you want international exposure in your program?", "question_study_plan": "📅 Would you like a personalized study plan?", "question_campus_map": "🗺️ Would you like to see university maps before deciding?", "option_pp": "Phnom Penh", "option_sr": "<PERSON><PERSON>", "option_btb": "Battambang", "option_any": "Any location", "option_low": "Low", "option_mid": "$500-$1500", "option_high": "High", "option_flex": "Flexible", "option_stem": "Science & Technology", "option_business": "Business", "option_health": "Health", "option_arts": "Arts", "option_social": "Social Sciences", "option_education": "Education", "option_tech": "Technology", "option_finance": "Finance", "option_gov": "Government", "option_entre": "Entrepreneurship", "option_unsure": "Not sure", "option_math": "Mathematics", "option_language": "Languages", "option_hands_on": "Practical", "option_all": "All subjects", "option_theory": "Theory", "option_practical": "Practical", "option_group": "Group", "option_self": "Self-study", "option_mixed": "Mixed", "option_full": "Full-time", "option_part": "Part-time", "option_evening": "Evening", "option_weekend": "Weekend", "option_kh": "Khmer", "option_en": "English", "option_both": "Both", "option_yes": "Yes", "option_partial": "Partial", "option_no": "No", "option_very": "Very important", "option_some": "Somewhat", "option_little": "A little", "option_none": "Not important", "option_sports": "Sports", "option_volunteer": "Volunteer", "option_clubs": "Clubs", "option_salary": "Salary", "option_stability": "Stability", "option_passion": "Passion", "option_growth": "Growth", "option_balance": "Work-life balance", "option_medium": "Medium", "option_maybe": "Maybe"}