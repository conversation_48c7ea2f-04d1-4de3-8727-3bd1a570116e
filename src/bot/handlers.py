"""
Telegram Bot Handlers Module
Manages conversation flow and user interactions
"""

from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes, Conversation<PERSON>andler
from typing import Dict, Any
import logging
import sys
from pathlib import Path

# Add project root to path for metrics import
sys.path.append(str(Path(__file__).parents[2]))
from src.infra.metrics import count_calls, track_latency, log_event
from .i18n import t, get_lang
from .utils import (
    cache_recommendations,
    cache_user_answers,
    format_recommendation_card,
    log_recommendation_metrics
)

# Conversation states
ASKING_QUESTIONS = 1
SHOWING_RESULTS = 2

logger = logging.getLogger(__name__)


def get_default_user_settings() -> dict:
    """
    Get default user settings

    Returns:
        dict: Default user settings with all filter options
    """
    return {
        "low_cost_only": False,
        "phnom_penh_only": False,
        "language": "kh"
    }


@count_calls("start")
async def start_assessment(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    """
    Start the assessment conversation

    Args:
        update: Telegram update object
        context: Bot context

    Returns:
        int: Next conversation state
    """
    from .keyboards import get_question_by_index, create_question_keyboard, create_progress_text

    # Initialize user session
    context.user_data['answers'] = {}
    context.user_data['current_question'] = 0

    # Get user language preference
    lang = get_lang(context.user_data)

    # Get first question
    question = get_question_by_index(0)
    keyboard = create_question_keyboard(question, lang)
    progress = create_progress_text(1, lang)

    # Send first question
    message = f"{t('assessment_title', lang)}\n\n{progress}\n\n{t(question['question_key'], lang)}"

    if update.callback_query:
        await update.callback_query.edit_message_text(
            text=message,
            reply_markup=keyboard
        )
    else:
        await update.message.reply_text(
            text=message,
            reply_markup=keyboard
        )

    return ASKING_QUESTIONS


async def handle_question_answer(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    """
    Handle user's answer to assessment question

    Args:
        update: Telegram update object
        context: Bot context

    Returns:
        int: Next conversation state
    """
    from .keyboards import get_question_by_index, create_question_keyboard, create_progress_text, ASSESSMENT_QUESTIONS

    query = update.callback_query
    await query.answer()

    # Parse callback data: "answer_question_id_option_value"
    callback_data = query.data
    if not callback_data.startswith("answer_"):
        return ASKING_QUESTIONS

    parts = callback_data.split("_", 2)
    if len(parts) < 3:
        return ASKING_QUESTIONS

    question_id = parts[1]
    option_value = parts[2]

    # Store answer
    context.user_data['answers'][question_id] = option_value

    # Move to next question
    current_index = context.user_data['current_question']
    next_index = current_index + 1

    if next_index >= len(ASSESSMENT_QUESTIONS):
        # All questions answered, show results
        return await show_recommendations(update, context)

    # Show next question
    context.user_data['current_question'] = next_index
    lang = get_lang(context.user_data)
    question = get_question_by_index(next_index)
    keyboard = create_question_keyboard(question, lang)
    progress = create_progress_text(next_index + 1, lang)

    message = f"{t('assessment_title', lang)}\n\n{progress}\n\n{t(question['question_key'], lang)}"

    await query.edit_message_text(
        text=message,
        reply_markup=keyboard
    )

    return ASKING_QUESTIONS


@track_latency("recommendation_latency")
async def show_recommendations(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    """
    Show final recommendations to user

    Args:
        update: Telegram update object
        context: Bot context

    Returns:
        int: Next conversation state
    """
    import sys
    from pathlib import Path

    # Add project root to path
    sys.path.append(str(Path(__file__).parents[2]))

    from src.core.mcda import recommender_mcda
    from src.core.data_loader import load_raw
    from src.core.feature_engineering import add_derived_features

    query = update.callback_query

    # Get user answers
    user_answers = context.user_data.get('answers', {})

    try:
        # Get hybrid recommendations (MCDA + ML)
        from src.core.hybrid_recommender import get_recommendations
        recommendations = get_recommendations(user_answers, top_k=5)

        # Get user language preference
        lang = get_lang(context.user_data)

        # Send completion message
        completion_text = f"{t('assessment_complete', lang)}\n\n{t('recommendations_title', lang)}"

        if query:
            await query.edit_message_text(
                text=completion_text
            )
        else:
            await update.message.reply_text(
                text=completion_text
            )

        # Send each recommendation as a separate message using utility function
        for i, program in enumerate(recommendations, 1):
            card_text = format_recommendation_card(program, i)

            # Create inline keyboard for each recommendation
            keyboard = [
                [
                    InlineKeyboardButton(
                        t('button_back_to_list', lang),
                        callback_data=f"back_to_list"
                    ),
                    InlineKeyboardButton(
                        t('button_why_recommended', lang),
                        callback_data=f"explain_rec:{program.get('major_id', 'unknown')}"
                    )
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            if query:
                await context.bot.send_message(
                    chat_id=query.message.chat_id,
                    text=card_text.strip(),
                    reply_markup=reply_markup
                )
            else:
                await update.message.reply_text(
                    text=card_text.strip(),
                    reply_markup=reply_markup
                )

        # Cache recommendations and user answers using utility functions
        cache_recommendations(recommendations, context)
        cache_user_answers(user_answers, context)

        # Log recommendation metrics
        log_recommendation_metrics(recommendations)
        log_event("recommendation_sent", {"count": len(recommendations)})

        # Send final message
        final_text = t("restart_message", lang)

        if query:
            await context.bot.send_message(
                chat_id=query.message.chat_id,
                text=final_text
            )
        else:
            await update.message.reply_text(text=final_text)

    except Exception as e:
        lang = get_lang(context.user_data)
        error_text = t("error_recommendation", lang).format(error=str(e))

        if query:
            await context.bot.send_message(
                chat_id=query.message.chat_id,
                text=error_text
            )
        else:
            await update.message.reply_text(text=error_text)

    # Clear user data to prevent memory leaks (but preserve cache for details)
    cache_recs = context.user_data.get("last_recs", {})
    cache_answers = context.user_data.get("last_answers", {})
    context.user_data.clear()
    context.user_data["last_recs"] = cache_recs
    context.user_data["last_answers"] = cache_answers

    return ConversationHandler.END


async def display_recommendations_list(update: Update, context: ContextTypes.DEFAULT_TYPE, recommendations: list, query=None) -> None:
    """
    Display a list of recommendations with inline buttons

    Args:
        update: Telegram update object
        context: Bot context
        recommendations: List of recommendation dictionaries
        query: Optional callback query for editing messages
    """
    lang = get_lang(context)

    # Send title message
    title_text = t('recommendations_title', lang)

    if query:
        await query.edit_message_text(text=title_text)
    else:
        await update.message.reply_text(text=title_text)

    # Send each recommendation as a separate message
    for i, program in enumerate(recommendations, 1):
        card_text = format_recommendation_card(program, i)

        # Create inline keyboard for each recommendation
        keyboard = [
            [
                InlineKeyboardButton(
                    t('button_back_to_list', lang),
                    callback_data=f"back_to_list"
                ),
                InlineKeyboardButton(
                    t('button_why_recommended', lang),
                    callback_data=f"explain_rec:{program.get('major_id', 'unknown')}"
                )
            ]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)

        if query:
            await context.bot.send_message(
                chat_id=query.message.chat_id,
                text=card_text.strip(),
                reply_markup=reply_markup
            )
        else:
            await update.message.reply_text(
                text=card_text.strip(),
                reply_markup=reply_markup
            )


async def handle_details_request(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """
    Handle request for detailed program information

    Args:
        update: Telegram update object
        context: Bot context
    """
    # This will be implemented in future versions
    lang = get_lang(context.user_data)
    await update.message.reply_text(
        t("details_coming_soon", lang)
    )


def create_conversation_handler() -> ConversationHandler:
    """
    Create the main conversation handler

    Returns:
        ConversationHandler: Configured conversation handler
    """
    from telegram.ext import CommandHandler, CallbackQueryHandler, MessageHandler, filters

    return ConversationHandler(
        entry_points=[
            CommandHandler('recommend', start_assessment),
            CommandHandler('start', start_assessment)
        ],
        states={
            ASKING_QUESTIONS: [
                CallbackQueryHandler(handle_question_answer, pattern=r'^answer_')
            ],
            SHOWING_RESULTS: [
                MessageHandler(filters.TEXT, handle_details_request)
            ]
        },
        fallbacks=[
            CommandHandler('cancel', cancel_conversation),
            CommandHandler('start', start_assessment)
        ]
    )


async def cancel_conversation(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    """Cancel the current conversation"""
    lang = get_lang(context.user_data)
    await update.message.reply_text(
        t("cancel_message", lang)
    )

    # Clear user data to prevent memory leaks
    context.user_data.clear()

    return ConversationHandler.END


async def back_to_list_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle 'Back to list' button callback"""
    query = update.callback_query
    await query.answer()

    lang = get_lang(context)

    # Get last recommendations from cache
    last_recs = context.user_data.get("last_recs", {})
    if not last_recs:
        await query.edit_message_text(
            text="❌ No previous recommendations found. Please run /start to get new recommendations."
        )
        return

    # Convert cache back to list format
    recommendations = list(last_recs.values())

    # Sort by hybrid score if available
    recommendations.sort(key=lambda x: x.get('hybrid_score', x.get('mcda_score', 0)), reverse=True)

    # Show recommendations list
    await display_recommendations_list(update, context, recommendations, query=query)


async def explain_rec_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle 'Why this recommendation?' button callback"""
    query = update.callback_query
    await query.answer()

    lang = get_lang(context)

    # Extract program ID from callback data
    callback_data = query.data
    if not callback_data.startswith("explain_rec:"):
        await query.edit_message_text(text="❌ Invalid callback data")
        return

    program_id = callback_data.split(":", 1)[1]

    # Get program from cache
    last_recs = context.user_data.get("last_recs", {})
    programme = last_recs.get(program_id)

    if not programme:
        await query.edit_message_text(text=f"❌ Program {program_id} not found in cache")
        return

    # Create explanation message
    mcda_score = programme.get('mcda_score', 0.0)
    ml_score = programme.get('ml_score', 0.0)
    hybrid_score = programme.get('hybrid_score', 0.0)
    reason = programme.get('mcda_reason', 'ការវាយតម្លៃតាមលក្ខណៈវិនិច្ឆ័យ')

    explanation_text = f"""{t('hybrid_score_explanation', lang)}

{t('score_breakdown', lang).format(
    mcda_score=mcda_score,
    ml_score=ml_score,
    hybrid_score=hybrid_score
)}

{t('why_recommended', lang).format(reason=reason)}

{t('mcda_explanation', lang)}
{t('ml_explanation', lang)}"""

    # Create back button
    keyboard = [[InlineKeyboardButton(t('button_back_to_list', lang), callback_data="back_to_list")]]
    reply_markup = InlineKeyboardMarkup(keyboard)

    await query.edit_message_text(
        text=explanation_text,
        reply_markup=reply_markup
    )
