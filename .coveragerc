[run]
omit =
    # Helper/legacy or runtime-only modules – not worth unit-testing
    src/bot/dry_run.py
    src/bot/error_handler.py
    src/bot/keyboards.py
    src/bot/app.py
    src/bot/commands.py
    src/bot/handlers.py
    src/bot/utils.py
    src/infra/exporter.py
    src/core/mcda_demo.py

[report]
# Regexes for lines to exclude from consideration
exclude_lines =
    # Have to re-enable the standard pragma
    pragma: no cover

    # Don't complain about missing debug-only code:
    def __repr__
    if self\.debug

    # Don't complain if tests don't hit defensive assertion code:
    raise AssertionError
    raise NotImplementedError

    # Don't complain if non-runnable code isn't run:
    if 0:
    if __name__ == .__main__.:

    # Don't complain about abstract methods, they aren't run:
    @(abc\.)?abstractmethod

ignore_errors = True

[html]
directory = htmlcov
