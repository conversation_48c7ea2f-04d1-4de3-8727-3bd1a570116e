"""
ML Tuning Tests
Tests for ML hyperparameter tuning and model improvements
"""

import pytest
import json
import subprocess
import sys
from pathlib import Path
import numpy as np

# Add project root to path
sys.path.append(str(Path(__file__).parents[1]))

from src.core.ml_reranker import <PERSON><PERSON><PERSON><PERSON>


def test_new_model_exists():
    """Test that new model file exists"""
    model_path = Path("models/rf_ranker_v3.joblib")

    if not model_path.exists():
        pytest.skip("New model not trained yet, run hyperparameter search and training first")

    assert model_path.exists(), "New model file should exist"
    assert model_path.stat().st_size > 0, "New model file should not be empty"


def test_new_model_loads():
    """Test that new model can be loaded"""
    model_path = "models/rf_ranker_v2.joblib"
    
    if not Path(model_path).exists():
        pytest.skip("New model not trained yet")
    
    reranker = <PERSON><PERSON><PERSON>ker(model_path)
    assert reranker.model is not None, "New model should load successfully"
    assert reranker.scaler is not None, "New model should have scaler"


def test_new_model_predicts_non_constant():
    """Test that new model predicts non-constant values (variance > 0)"""
    model_path = "models/rf_ranker_v3.joblib"
    
    if not Path(model_path).exists():
        pytest.skip("New model not trained yet")
    
    # Load model
    reranker = MLReranker(model_path)
    
    # Load some real programs for testing
    from src.core.data_loader import load_raw
    from src.core.feature_engineering import add_derived_features
    from src.core.mcda import score
    
    raw_progs = load_raw()
    if len(raw_progs) == 0:
        pytest.skip("No data files found in data/raw directory")
    progs = add_derived_features(raw_progs)[:20]  # First 20 programs
    
    # Add MCDA scores to programs
    user_answers = {
        "location_preference": "pp", 
        "budget_range": "mid", 
        "field_of_interest": "stem"
    }
    
    for prog in progs:
        prog['mcda_score'] = score(user_answers, prog)
    
    # Rank programs
    ranked_progs = reranker.rank(user_answers, progs, top_k=10)
    
    # Extract ML scores
    ml_scores = [p["ml_score"] for p in ranked_progs]
    
    # Check variance
    score_variance = np.var(ml_scores)
    assert score_variance > 0, f"ML scores should have variance > 0, got {score_variance}"
    
    # Check range
    score_range = max(ml_scores) - min(ml_scores)
    assert score_range > 0.01, f"ML scores should have range > 0.01, got {score_range}"


def test_hyperparameter_search_results():
    """Test that hyperparameter search produces valid results"""
    results_path = Path("build/hp_results.json")
    
    if not results_path.exists():
        pytest.skip("Hyperparameter search not run yet")
    
    with open(results_path, 'r') as f:
        results = json.load(f)
    
    # Check structure (updated for multi-model search)
    assert 'best_params' in results, "Results should contain best_params"
    assert 'best_cv_r2' in results, "Results should contain best_cv_r2"
    assert 'all_model_results' in results, "Results should contain all_model_results"
    
    # Check best params
    best_params = results['best_params']
    assert 'n_estimators' in best_params, "Best params should contain n_estimators"
    assert 'max_depth' in best_params, "Best params should contain max_depth"
    
    # Check values are in expected ranges (updated for multi-model search)
    assert 'n_estimators' in best_params, "Best params should contain n_estimators"
    assert 'max_depth' in best_params, "Best params should contain max_depth"
    
    # Check R² is reasonable
    assert 0 <= results['best_cv_r2'] <= 1, "R² should be between 0 and 1"


def test_evaluation_report_exists():
    """Test that evaluation report exists and shows improvement"""
    eval_path = Path("build/ml_eval.md")
    
    if not eval_path.exists():
        pytest.skip("Evaluation not run yet")
    
    assert eval_path.exists(), "Evaluation report should exist"
    
    # Read content
    with open(eval_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check for required sections
    assert "Model Comparison" in content, "Report should contain model comparison"
    assert "| Model | R² | RMSE |" in content, "Report should contain results table"
    assert "v1 (old)" in content, "Report should mention old model"
    assert "v2 (new)" in content, "Report should mention new model"


def test_evaluation_meets_target():
    """Test that evaluation shows ΔR² ≥ +10%"""
    eval_path = Path("build/ml_eval.md")
    
    if not eval_path.exists():
        pytest.skip("Evaluation not run yet")
    
    # Read content
    with open(eval_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check for success indicator
    assert "✅ **SUCCESS**" in content, "Evaluation should show success (ΔR² ≥ +10%)"
    assert "ΔR² ≥ +10%" in content, "Report should mention target achievement"


def test_feature_importance_saved():
    """Test that feature importance is saved to CSV"""
    importance_path = Path("build/feature_importance.csv")
    
    if not importance_path.exists():
        pytest.skip("Feature importance not saved yet")
    
    assert importance_path.exists(), "Feature importance CSV should exist"
    
    # Check content
    import pandas as pd
    df = pd.read_csv(importance_path)
    
    assert 'feature' in df.columns, "CSV should have feature column"
    assert 'importance' in df.columns, "CSV should have importance column"
    assert len(df) > 0, "CSV should contain feature importance data"
    
    # Check that importances sum to approximately 1
    total_importance = df['importance'].sum()
    assert 0.95 <= total_importance <= 1.05, f"Importances should sum to ~1, got {total_importance}"


def test_hp_search_script_runs():
    """Test that hyperparameter search script can run"""
    # Check if training data exists
    if not Path("build/synth_train.csv").exists():
        pytest.skip("Training data not available")
    
    # This is a basic test that the script doesn't crash immediately
    # Full execution is tested in acceptance checklist
    try:
        result = subprocess.run([
            sys.executable, "scripts/hp_search.py", "--help"
        ], capture_output=True, text=True, timeout=10)
        assert result.returncode == 0, "HP search script should show help without error"
    except subprocess.TimeoutExpired:
        pytest.fail("HP search script help should not timeout")


def test_eval_script_runs():
    """Test that evaluation script can run"""
    # This is a basic test that the script doesn't crash immediately
    try:
        result = subprocess.run([
            sys.executable, "scripts/eval_ml.py", "--help"
        ], capture_output=True, text=True, timeout=10)
        assert result.returncode == 0, "Eval script should show help without error"
    except subprocess.TimeoutExpired:
        pytest.fail("Eval script help should not timeout")
