"""
Final Coverage Tests
Very targeted tests to cover the remaining missing lines to reach 95%
"""

import pytest
import sys
from pathlib import Path
from unittest.mock import patch, MagicMock, mock_open
import os
import numpy as np

# Add project root to path
sys.path.append(str(Path(__file__).parents[1]))


class TestFinalCoverage:
    """Test cases to cover the final missing lines"""
    
    def test_mcda_load_weights_yaml_error(self):
        """Test load_weights with YAML parsing error (lines 34-51)"""
        from src.core.mcda import load_weights
        
        # Mock file exists but YAML parsing fails
        with patch('pathlib.Path.exists', return_value=True):
            with patch('builtins.open', mock_open(read_data='invalid: yaml: [content')):
                with patch('yaml.safe_load', side_effect=Exception("YAML error")):
                    weights = load_weights()
                    # Should return default weights
                    assert isinstance(weights, dict)
                    assert len(weights) > 0
    
    def test_mcda_score_with_specific_bonuses(self):
        """Test MCDA score with specific bonus calculations (lines 240-244, 267-271)"""
        from src.core.mcda import score
        
        # Test ROI bonus calculation
        user_answers = {
            'location_preference': 'pp',
            'budget_range': 'low',
            'field_of_interest': 'stem',
            'career_goal': 'high_salary'
        }
        
        programme = {
            'major_name_en': 'Computer Science',
            'city': 'ភ្នំពេញ',
            'field_tag': 'STEM',
            'tuition_bracket': 'Low',
            'employment_rate': '95%',
            'tuition_fees_usd': '500'
        }
        
        result = score(user_answers, programme)
        assert isinstance(result, float)
        assert 0.0 <= result <= 1.0
        
        # Test scholarship bonus
        programme_with_scholarship = programme.copy()
        programme_with_scholarship['has_scholarship'] = True
        programme_with_scholarship['scholarship_availability'] = True
        
        result_with_scholarship = score(user_answers, programme_with_scholarship)
        assert isinstance(result_with_scholarship, float)
        assert 0.0 <= result_with_scholarship <= 1.0
    
    def test_mcda_score_vectorized_with_numpy(self):
        """Test score_vectorized with numpy operations (lines 289-306)"""
        from src.core.mcda import score_vectorized
        
        user_answers = {
            'location_preference': 'pp',
            'budget_range': 'mid',
            'field_of_interest': 'stem'
        }
        
        programmes = [
            {
                'major_name_en': 'Computer Science',
                'city': 'ភ្នំពេញ',
                'field_tag': 'STEM',
                'tuition_bracket': 'Medium'
            },
            {
                'major_name_en': 'Engineering',
                'city': 'សៀមរាប',
                'field_tag': 'STEM',
                'tuition_bracket': 'High'
            }
        ]
        
        result = score_vectorized(user_answers, programmes)
        assert len(result) == 2
        assert all(isinstance(score, (float, np.floating)) for score in result)
        assert all(0.0 <= score <= 1.0 for score in result)
    
    def test_mcda_score_with_language_preference(self):
        """Test MCDA score with language preference bonus (lines 333, 344, 355)"""
        from src.core.mcda import score
        
        user_answers = {
            'location_preference': 'pp',
            'budget_range': 'mid',
            'field_of_interest': 'business',
            'language_preference': 'khmer'
        }
        
        # Programme with Khmer language instruction
        programme = {
            'major_name_en': 'Business Administration',
            'city': 'ភ្នំពេញ',
            'field_tag': 'Business',
            'tuition_bracket': 'Medium',
            'language_of_instruction': ['Khmer', 'English']
        }
        
        result = score(user_answers, programme)
        assert isinstance(result, float)
        assert 0.0 <= result <= 1.0
        
        # Test with English preference
        user_answers['language_preference'] = 'english'
        programme['language_of_instruction'] = ['English']
        
        result_english = score(user_answers, programme)
        assert isinstance(result_english, float)
        assert 0.0 <= result_english <= 1.0
    
    def test_ml_reranker_scaler_transform_error(self):
        """Test MLReranker when scaler transform fails (lines 77-82)"""
        from src.core.ml_reranker import MLReranker
        
        # Mock model and scaler with scaler failing
        mock_model = MagicMock()
        mock_scaler = MagicMock()
        mock_scaler.transform.side_effect = Exception("Scaler error")
        
        reranker = MLReranker("fake_model.joblib")
        reranker.model = mock_model
        reranker.scaler = mock_scaler
        
        user_answers = {'location_preference': 'pp'}
        programmes = [{'major_id': 'test1', 'major_name_en': 'Test 1', 'mcda_score': 0.8}]
        
        # Should fallback to dummy scores when scaler fails
        result = reranker.rank(user_answers, programmes, top_k=1)
        
        assert len(result) == 1
        assert result[0]['ml_score'] == 0.5
    
    def test_ml_reranker_model_predict_error(self):
        """Test MLReranker when model predict fails (lines 93, 95)"""
        from src.core.ml_reranker import MLReranker
        
        # Mock model and scaler with model prediction failing
        mock_model = MagicMock()
        mock_model.predict.side_effect = Exception("Prediction error")
        mock_scaler = MagicMock()
        mock_scaler.transform.return_value = [[0.5, 0.5, 0.5]]
        
        reranker = MLReranker("fake_model.joblib")
        reranker.model = mock_model
        reranker.scaler = mock_scaler
        
        user_answers = {'location_preference': 'pp'}
        programmes = [{'major_id': 'test1', 'major_name_en': 'Test 1', 'mcda_score': 0.8}]
        
        # Should fallback to dummy scores when prediction fails
        result = reranker.rank(user_answers, programmes, top_k=1)
        
        assert len(result) == 1
        assert result[0]['ml_score'] == 0.5
    
    def test_metrics_async_wrapper_coverage(self):
        """Test metrics async wrapper code paths (lines 74-88)"""
        from src.infra.metrics import track_latency, count_calls
        import asyncio
        import inspect
        
        # Create a function that looks like async to trigger the async detection
        def fake_async_function():
            return "result"
        
        # Manually set __code__ to look like async function
        fake_async_function.__code__ = type(lambda: None).__code__
        
        # Apply decorators
        decorated_latency = track_latency("test")(fake_async_function)
        decorated_calls = count_calls("test")(fake_async_function)
        
        # Call the decorated functions
        result1 = decorated_latency()
        result2 = decorated_calls()
        
        assert result1 == "result"
        assert result2 == "result"
    
    def test_metrics_file_read_error_handling(self):
        """Test metrics file read error handling (lines 119-126)"""
        from src.infra.metrics import read_metrics, METRICS_FILE
        
        # Create a file that exists but can't be read
        with patch('pathlib.Path.exists', return_value=True):
            with patch('builtins.open', side_effect=IOError("Cannot read file")):
                metrics = read_metrics()
                # Should return empty list when file can't be read
                assert isinstance(metrics, list)
                assert len(metrics) == 0
    
    def test_hybrid_recommender_import_error_handling(self):
        """Test hybrid recommender import error handling (lines 18-21)"""
        from src.core.hybrid_recommender import get_recommendations
        
        # Mock load_raw to raise ImportError
        with patch('src.core.hybrid_recommender.load_raw', side_effect=ImportError("Import failed")):
            user_answers = {'location_preference': 'pp'}
            recommendations = get_recommendations(user_answers)
            # Should return empty list when import fails
            assert isinstance(recommendations, list)
            assert len(recommendations) == 0
    
    def test_hybrid_recommender_ml_version_fallback(self):
        """Test hybrid recommender ML version fallback (line 146)"""
        from src.core.hybrid_recommender import _get_ml_scores
        
        programmes = [{'major_id': 'test1', 'major_name_en': 'Test 1'}]
        user_answers = {'location_preference': 'pp'}
        
        # Test with no ML_VERSION set (should default to version 3)
        with patch.dict('os.environ', {}, clear=True):
            with patch('pathlib.Path.exists', return_value=False):
                scores = _get_ml_scores(user_answers, programmes)
                assert len(scores) == 1
                assert scores[0] == 0.5


if __name__ == '__main__':
    pytest.main([__file__])
