"""
Comprehensive Coverage Tests
Tests to cover remaining missing lines across modules
"""

import sys
from pathlib import Path
from unittest.mock import patch, MagicMock
import tempfile
import json

# Add project root to path
sys.path.append(str(Path(__file__).parents[1]))

from src.core.mcda import load_weights, generate_reason
from src.infra.metrics import histogram, clear_metrics, main
from src.core.hybrid_recommender import generate_reason as hybrid_reason


def test_load_weights_yaml_error():
    """Test load_weights with YAML parsing error"""
    with patch("pathlib.Path.exists", return_value=True):
        with patch("builtins.open", side_effect=Exception("File error")):
            weights = load_weights()
            # Should return default weights on error
            assert isinstance(weights, dict)
            assert "question_weights" in weights


def test_generate_reason_comprehensive():
    """Test generate_reason with various inputs"""
    user_answers = {
        "location_preference": "pp",
        "budget_range": "low",
        "field_of_interest": "stem"
    }
    
    programme = {
        "major_name_en": "Computer Science",
        "university_name_en": "Test University",
        "city": "ភ្នំពេញ",
        "field_tag": "STEM",
        "tuition_bracket": "Low"
    }
    
    reason = generate_reason(user_answers, programme, 0.85)
    assert isinstance(reason, str)
    assert len(reason) > 0
    # The reason is in Khmer, so just check it's not empty
    assert "0.85" in reason or len(reason) > 5


def test_histogram_function():
    """Test histogram logging function"""
    with tempfile.NamedTemporaryFile(mode='w+', delete=False) as f:
        with patch("src.infra.metrics.METRICS_FILE", Path(f.name)):
            histogram("test_histogram", 42.5)
            
            # Read back the metric
            f.seek(0)
            line = f.readline().strip()
            metric = json.loads(line)
            
            assert metric["event"] == "histogram"
            assert metric["data"]["name"] == "test_histogram"
            assert metric["data"]["value"] == 42.5


def test_clear_metrics_function():
    """Test clear_metrics function"""
    with tempfile.NamedTemporaryFile(delete=False) as f:
        with patch("src.infra.metrics.METRICS_FILE", Path(f.name)):
            # File exists, should be deleted
            clear_metrics()
            assert not Path(f.name).exists()


def test_main_flask_not_available():
    """Test main function when Flask is not available"""
    with patch("src.infra.metrics._lazy_import_flask", return_value=(None, None)):
        # Should return early when Flask is not available
        result = main()
        assert result is None  # Function returns None when Flask unavailable


def test_hybrid_reason_generation():
    """Test hybrid recommender reason generation"""
    user_answers = {"location_preference": "pp"}
    programme = {"major_name_en": "Test Major"}
    
    reason = hybrid_reason(user_answers, programme, 0.75)
    assert isinstance(reason, str)
    assert len(reason) > 0


def test_mcda_edge_cases():
    """Test MCDA edge cases"""
    from src.core.mcda import score_vectorized
    
    # Test with programmes missing required fields
    user_answers = {"location_preference": "pp"}
    programmes = [
        {"major_id": "test1"},  # Missing most fields
        {"major_id": "test2", "city": "unknown"}  # Unknown city
    ]
    
    scores = score_vectorized(user_answers, programmes)
    assert isinstance(scores, list)
    assert len(scores) == 2
    assert all(isinstance(score, (int, float)) for score in scores)


def test_ml_reranker_edge_cases():
    """Test ML reranker edge cases"""
    from src.core.ml_reranker import MLReranker
    
    # Test with non-existent model file
    reranker = MLReranker("definitely_does_not_exist.joblib")
    
    programmes = [{"major_id": "test", "mcda_score": 0.8}]
    result = reranker.rank({}, programmes)
    
    assert len(result) == 1
    assert result[0]["ml_score"] == 0.5  # Default score when no model


def test_mcda_load_weights_file_error():
    """Test load_weights with file read error"""
    with patch("pathlib.Path.exists", return_value=True):
        with patch("yaml.safe_load", side_effect=Exception("YAML error")):
            weights = load_weights()
            assert isinstance(weights, dict)


def test_mcda_missing_weights():
    """Test MCDA scoring with missing weights"""
    from src.core.mcda import score_vectorized

    # Mock load_weights to return empty weights
    with patch("src.core.mcda.load_weights", return_value={"question_weights": {}}):
        user_answers = {"location_preference": "pp"}
        programmes = [{"major_id": "test", "city": "ភ្នំពេញ"}]

        scores = score_vectorized(user_answers, programmes)
        assert isinstance(scores, list)
        assert len(scores) == 1


def test_metrics_format_prometheus_with_data():
    """Test format_prometheus_metrics with actual data"""
    from src.infra.metrics import format_prometheus_metrics

    mock_metrics = [
        {"event": "histogram", "data": {"name": "test_metric", "value": 42.0}},
        {"event": "other", "data": {"name": "ignored", "value": 1.0}}
    ]

    with patch("src.infra.metrics.read_metrics", return_value=mock_metrics):
        output = format_prometheus_metrics()
        assert "test_metric 42.0" in output
        assert "ignored" not in output


def test_hybrid_recommender_empty_data():
    """Test hybrid recommender with empty enhanced data"""
    from src.core.hybrid_recommender import get_recommendations

    with patch("src.core.hybrid_recommender.load_raw", return_value=[]):
        with patch("src.core.feature_engineering.add_derived_features", return_value=[]):
            result = get_recommendations({"location_preference": "pp"})
            assert result == []


def test_mcda_vectorized_edge_cases():
    """Test MCDA vectorized functions with edge cases"""
    from src.core.mcda import score_vectorized

    # Test with programmes that have unusual field values
    user_answers = {
        "location_preference": "unknown_location",
        "budget_range": "invalid_budget",
        "field_of_interest": "unknown_field"
    }

    programmes = [
        {
            "major_id": "test1",
            "city": "unknown_city",
            "field_tag": "unknown_field",
            "tuition_bracket": "unknown_bracket"
        }
    ]

    scores = score_vectorized(user_answers, programmes)
    assert isinstance(scores, list)
    assert len(scores) == 1
    assert 0.0 <= scores[0] <= 1.0


def test_mcda_weights_loading_edge_cases():
    """Test MCDA weights loading with various edge cases"""
    from src.core.mcda import load_weights

    # Test with corrupted YAML
    with patch("pathlib.Path.exists", return_value=True):
        with patch("builtins.open", side_effect=FileNotFoundError):
            weights = load_weights()
            assert isinstance(weights, dict)
            assert "question_weights" in weights


def test_ml_reranker_feature_extraction_errors():
    """Test ML reranker with feature extraction errors"""
    from src.core.ml_reranker import MLReranker

    reranker = MLReranker("fake.joblib")
    reranker.model = None
    reranker.scaler = None

    # Test with programmes that might cause feature extraction issues
    programmes = [
        {"major_id": "test1"},  # Minimal data
        {"major_id": "test2", "invalid_field": "value"}  # Unexpected fields
    ]

    result = reranker.rank({}, programmes)
    assert len(result) == 2
    assert all(prog["ml_score"] == 0.5 for prog in result)
