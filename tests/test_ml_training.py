"""
ML Training Tests
Tests for ML re-ranker training and ranking functionality
"""

import pytest
import pandas as pd
import tempfile
from pathlib import Path
import sys

# Add project root to path
sys.path.append(str(Path(__file__).parents[1]))

from src.core.ml_reranker import <PERSON><PERSON>eran<PERSON>
from src.core.feature_schema import FEATURES
from scripts.train_ml import train


def create_dummy_training_data(tmp_path):
    """Create dummy training data for testing"""
    # Create minimal training dataset
    data = []
    for i in range(100):
        example = {
            'answer_json': '{"location_preference": "pp", "budget_range": "mid"}',
            'programme_id': f'prog_{i}',
            'label': 1.0 if i < 10 else 0.0,  # Top 10% are positive
        }
        
        # Add feature values
        for j, feat in enumerate(FEATURES):
            if feat == 'mcda_score':
                example[feat] = 0.8 if i < 10 else 0.3  # Higher for positive examples
            elif feat == 'location_pref==prog':
                example[feat] = 1.0 if i % 2 == 0 else 0.0
            elif feat == 'budget_bracket_diff':
                example[feat] = float(i % 3)
            elif feat == 'field_match':
                example[feat] = 1.0 if i < 20 else 0.0
            elif feat == 'has_scholarship':
                example[feat] = 1.0 if i % 4 == 0 else 0.0
            elif feat == 'employment_rate':
                example[feat] = 0.85 if i < 30 else 0.65
            elif feat == 'tuition_usd':
                example[feat] = 1000.0 + (i * 10)
            else:
                example[feat] = 0.5
        
        data.append(example)
    
    # Save to CSV
    csv_path = tmp_path / "dummy_train.csv"
    df = pd.DataFrame(data)
    df.to_csv(csv_path, index=False)
    
    return str(csv_path)


def test_training_runs(tmp_path):
    """Test that training runs without errors"""
    # Create dummy data
    data_path = create_dummy_training_data(tmp_path)
    model_path = tmp_path / "model.joblib"
    
    # Train model
    train(data_path, str(model_path))
    
    # Check model file exists
    assert model_path.exists()
    
    # Check model can be loaded
    reranker = MLReranker(str(model_path))
    assert reranker.model is not None
    assert reranker.scaler is not None


def test_rank_variance():
    """Test that ML ranking produces varied scores"""
    # Use the latest trained model (v3) if available, fallback to v1
    model_path = "models/rf_ranker_v3.joblib"
    if not Path(model_path).exists():
        model_path = "models/rf_ranker.joblib"

    if not Path(model_path).exists():
        pytest.skip("Trained model not available, run training first")

    # Load model
    reranker = MLReranker(model_path)

    # Load some real programs for testing
    from src.core.data_loader import load_raw
    from src.core.feature_engineering import add_derived_features
    from src.core.mcda import score

    raw_progs = load_raw()
    if len(raw_progs) == 0:
        pytest.skip("No data files found in data/raw directory")
    progs = add_derived_features(raw_progs)[:30]  # First 30 programs

    # Add MCDA scores to programs
    user_answers = {
        "location_preference": "pp",
        "budget_range": "mid",
        "field_of_interest": "stem"
    }

    for prog in progs:
        prog['mcda_score'] = score(user_answers, prog)

    # Rank programs
    ranked_progs = reranker.rank(user_answers, progs, top_k=10)

    # Extract ML scores
    ml_scores = [p["ml_score"] for p in ranked_progs]

    # Check variance (more lenient threshold)
    score_range = max(ml_scores) - min(ml_scores)
    assert score_range > 0.01, f"ML scores too similar: range={score_range}"

    # Check that we got the expected number of results
    assert len(ranked_progs) == 10

    # Check that all programs have ml_score
    for prog in ranked_progs:
        assert 'ml_score' in prog
        assert isinstance(prog['ml_score'], (int, float))


def test_feature_extraction():
    """Test feature extraction functionality"""
    from src.core.feature_schema import extract_features
    
    user_answers = {
        "location_preference": "pp",
        "budget_range": "mid", 
        "field_of_interest": "stem"
    }
    
    programme = {
        'mcda_score': 0.75,
        'city': 'ភ្នំពេញ',
        'tuition_bracket': 'Medium',
        'field_tag': 'STEM',
        'has_scholarship': True,
        'employment_rate': '85%',
        'tuition_fees_usd': '$1,200'
    }
    
    features = extract_features(user_answers, programme)
    
    # Check feature count matches schema (updated for enriched features)
    assert len(features) == len(FEATURES), f"Expected {len(FEATURES)} features, got {len(features)}"
    
    # Check specific feature values
    assert features[0] == 0.75  # mcda_score
    assert features[1] == 1.0   # location match (pp -> ភ្នំពេញ)
    assert features[2] == 0.0   # budget diff (mid -> Medium = 0)
    assert features[3] == 1.0   # field match (stem -> STEM)
    assert features[4] == 1.0   # has_scholarship
    assert features[5] == 0.85  # employment_rate (85%)
    assert features[6] == 1200.0  # tuition_usd


def test_ml_reranker_fallback():
    """Test that ML reranker falls back gracefully when model not available"""
    # Create reranker without model
    reranker = MLReranker(model_path="nonexistent_model.joblib")
    
    user_answers = {"location_preference": "pp"}
    programmes = [
        {"major_id": "prog1", "university_name": "Test Uni 1"},
        {"major_id": "prog2", "university_name": "Test Uni 2"}
    ]
    
    # Should not crash and return programs with dummy scores
    ranked = reranker.rank(user_answers, programmes, top_k=2)
    
    assert len(ranked) == 2
    for prog in ranked:
        assert 'ml_score' in prog
        assert prog['ml_score'] == 0.5  # Dummy score
