"""
MCDA Score Math Tests
Tests for vectorized MCDA scoring calculations
"""

import pytest
import numpy as np
import sys
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parents[2]))

from src.core.mcda import (
    score_vectorized, 
    score,
    _calculate_location_scores_vectorized,
    _calculate_budget_scores_vectorized,
    _calculate_field_scores_vectorized,
    _safe_float
)


@pytest.fixture
def sample_user_answers():
    """Sample user answers for testing"""
    return {
        'location_preference': 'pp',
        'budget_range': 'low',
        'field_of_interest': 'stem',
        'scholarship_need': 'yes'
    }


@pytest.fixture
def sample_programmes():
    """Sample programme data for testing"""
    return [
        {
            'major_id': 'cs-001',
            'major_name_kh': 'វិទ្យាសាស្ត្រកុំព្យូទ័រ',
            'major_name_en': 'Computer Science',
            'city': 'ភ្នំពេញ',
            'tuition_fees_usd': 400,
            'employment_rate': 90,
            'scholarship_availability': True,
            'is_phnom_penh': True,
            'is_siem_reap': False,
            'is_battambang': False,
            'is_stem': True,
            'is_business': False,
            'is_arts': False,
            'is_low_cost': True,
            'is_medium_cost': False,
            'is_high_cost': False
        },
        {
            'major_id': 'bus-001',
            'major_name_kh': 'គ្រប់គ្រងអាជីវកម្ម',
            'major_name_en': 'Business Management',
            'city': 'សៀមរាប',
            'tuition_fees_usd': 800,
            'employment_rate': 75,
            'scholarship_availability': False,
            'is_phnom_penh': False,
            'is_siem_reap': True,
            'is_battambang': False,
            'is_stem': False,
            'is_business': True,
            'is_arts': False,
            'is_low_cost': False,
            'is_medium_cost': True,
            'is_high_cost': False
        },
        {
            'major_id': 'art-001',
            'major_name_kh': 'សិល្បៈ',
            'major_name_en': 'Fine Arts',
            'city': 'បាត់ដំបង',
            'tuition_fees_usd': 1200,
            'employment_rate': 60,
            'scholarship_availability': True,
            'is_phnom_penh': False,
            'is_siem_reap': False,
            'is_battambang': True,
            'is_stem': False,
            'is_business': False,
            'is_arts': True,
            'is_low_cost': False,
            'is_medium_cost': False,
            'is_high_cost': True
        }
    ]


class TestSafeFloat:
    """Test safe float conversion"""
    
    def test_safe_float_string_numbers(self):
        """Test conversion of string numbers"""
        assert _safe_float("123.45") == 123.45
        assert _safe_float("1,000") == 1000.0
        assert _safe_float("$500") == 500.0
        assert _safe_float("85%") == 85.0
    
    def test_safe_float_edge_cases(self):
        """Test edge cases"""
        assert _safe_float("") == 0.0
        assert _safe_float(None) == 0.0
        assert _safe_float("invalid") == 0.0
        assert _safe_float(42) == 42.0


class TestVectorizedScoring:
    """Test vectorized MCDA scoring"""
    
    def test_score_vectorized_basic(self, sample_user_answers, sample_programmes):
        """Test basic vectorized scoring"""
        scores = score_vectorized(sample_user_answers, sample_programmes)

        assert isinstance(scores, list)
        assert len(scores) == len(sample_programmes)
        assert all(0.0 <= score <= 1.0 for score in scores)

    def test_score_vectorized_empty_programmes(self, sample_user_answers):
        """Test with empty programmes list"""
        scores = score_vectorized(sample_user_answers, [])
        assert isinstance(scores, list)
        assert len(scores) == 0
    
    def test_score_vectorized_vs_single_score(self, sample_user_answers, sample_programmes):
        """Test that vectorized scores match single scores"""
        vectorized_scores = score_vectorized(sample_user_answers, sample_programmes)
        
        for i, programme in enumerate(sample_programmes):
            single_score = score(sample_user_answers, programme)
            # Allow small floating point differences
            assert abs(vectorized_scores[i] - single_score) < 1e-10
    
    def test_score_preference_matching(self, sample_programmes):
        """Test that scores reflect user preferences correctly"""
        # User prefers Phnom Penh, low budget, STEM
        user_answers = {
            'location_preference': 'pp',
            'budget_range': 'low', 
            'field_of_interest': 'stem'
        }
        
        scores = score_vectorized(user_answers, sample_programmes)
        
        # First programme (CS in PP, low cost, STEM) should score highest
        assert scores[0] > scores[1]  # CS > Business
        assert scores[0] > scores[2]  # CS > Arts


class TestLocationScoring:
    """Test location scoring component"""
    
    def test_location_scores_perfect_match(self):
        """Test perfect location match"""
        user_answers = {'location_preference': 'pp'}
        weights = {'question_weights': {'location_preference': 4.5}}
        
        is_pp = np.array([True, False, False])
        is_sr = np.array([False, True, False])
        is_btb = np.array([False, False, True])
        
        scores = _calculate_location_scores_vectorized(
            user_answers, weights, is_pp, is_sr, is_btb
        )
        
        assert scores[0] == 4.5  # Perfect match
        assert scores[1] == 4.5 * 0.7  # Partial match (major city)
        assert scores[2] == 4.5 * 0.7  # Partial match (major city)
    
    def test_location_scores_no_weight(self):
        """Test location scoring with zero weight"""
        user_answers = {'location_preference': 'pp'}
        weights = {'question_weights': {}}  # No location weight
        
        is_pp = np.array([True, False])
        is_sr = np.array([False, True])
        is_btb = np.array([False, False])
        
        scores = _calculate_location_scores_vectorized(
            user_answers, weights, is_pp, is_sr, is_btb
        )
        
        assert all(score == 0.0 for score in scores)


class TestBudgetScoring:
    """Test budget scoring component"""
    
    def test_budget_scores_matching(self):
        """Test budget preference matching"""
        user_answers = {'budget_range': 'low'}
        weights = {'question_weights': {'budget_range': 5.0}}
        
        is_low = np.array([True, False, False])
        is_medium = np.array([False, True, False])
        is_high = np.array([False, False, True])
        
        scores = _calculate_budget_scores_vectorized(
            user_answers, weights, is_low, is_medium, is_high
        )
        
        assert scores[0] == 5.0  # Perfect match
        assert scores[1] == 5.0 * 0.3  # Mismatch penalty
        assert scores[2] == 5.0 * 0.3  # Mismatch penalty


class TestFieldScoring:
    """Test field scoring component"""
    
    def test_field_scores_stem_preference(self):
        """Test STEM field preference"""
        user_answers = {'field_of_interest': 'stem'}
        weights = {'question_weights': {'field_of_interest': 4.8}}
        
        is_stem = np.array([True, False, False])
        is_business = np.array([False, True, False])
        is_arts = np.array([False, False, True])
        
        scores = _calculate_field_scores_vectorized(
            user_answers, weights, is_stem, is_business, is_arts
        )
        
        assert scores[0] == 4.8  # Perfect STEM match
        assert scores[1] == 4.8 * 0.2  # Non-STEM penalty
        assert scores[2] == 4.8 * 0.2  # Non-STEM penalty


class TestScoreNormalization:
    """Test score normalization"""
    
    def test_scores_in_valid_range(self, sample_user_answers, sample_programmes):
        """Test that all scores are in valid 0-1 range"""
        scores = score_vectorized(sample_user_answers, sample_programmes)
        
        assert all(0.0 <= score <= 1.0 for score in scores)
    
    def test_score_ordering_consistency(self, sample_programmes):
        """Test that score ordering is consistent with preferences"""
        # Test multiple user preference combinations
        test_cases = [
            {
                'location_preference': 'pp',
                'budget_range': 'low',
                'field_of_interest': 'stem'
            },
            {
                'location_preference': 'sr', 
                'budget_range': 'medium',
                'field_of_interest': 'business'
            }
        ]
        
        for user_answers in test_cases:
            scores = score_vectorized(user_answers, sample_programmes)
            
            # Scores should be deterministic and consistent
            assert len(scores) == 3
            assert all(isinstance(score, (int, float)) for score in scores)


class TestEdgeCases:
    """Test edge cases and error handling"""
    
    def test_missing_derived_features(self, sample_user_answers):
        """Test handling of programmes missing derived features"""
        incomplete_programme = {
            'major_id': 'test-001',
            'major_name_kh': 'ការសាកល្បង',
            'city': 'ភ្នំពេញ'
            # Missing derived features like is_phnom_penh, is_stem, etc.
        }
        
        scores = score_vectorized(sample_user_answers, [incomplete_programme])
        
        # Should not crash and return valid score
        assert len(scores) == 1
        assert 0.0 <= scores[0] <= 1.0
    
    def test_empty_user_answers(self, sample_programmes):
        """Test with empty user answers"""
        scores = score_vectorized({}, sample_programmes)
        
        # Should return neutral scores
        assert len(scores) == len(sample_programmes)
        assert all(0.0 <= score <= 1.0 for score in scores)
    
    def test_invalid_user_preferences(self, sample_programmes):
        """Test with invalid user preference values"""
        invalid_answers = {
            'location_preference': 'invalid_location',
            'budget_range': 'invalid_budget',
            'field_of_interest': 'invalid_field'
        }
        
        scores = score_vectorized(invalid_answers, sample_programmes)
        
        # Should handle gracefully and return valid scores
        assert len(scores) == len(sample_programmes)
        assert all(0.0 <= score <= 1.0 for score in scores)
