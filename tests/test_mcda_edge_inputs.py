"""
MCDA Edge Input Tests
Tests for edge cases in MCDA scoring
"""

import sys
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parents[1]))

from src.core.mcda import score_vectorized, score


def test_vectorized_none_or_empty():
    assert score_vectorized({}, None) == []
    assert score_vectorized({}, []) == []


def test_score_none_inputs():
    assert score(None, None) == 0.0
