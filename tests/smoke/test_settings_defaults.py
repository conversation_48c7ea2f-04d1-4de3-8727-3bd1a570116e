"""
Smoke tests for settings defaults
"""

import sys
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parents[2]))

from src.bot.handlers import get_default_user_settings


def test_default_settings_all_keys_present():
    """Test that default settings contain all expected keys"""
    settings = get_default_user_settings()
    
    # Check that all required filter keys are present
    assert {"low_cost_only", "phnom_penh_only"} <= settings.keys()
    
    # Check that language key is present
    assert "language" in settings
    
    # Check default values
    assert settings["low_cost_only"] is False
    assert settings["phnom_penh_only"] is False
    assert settings["language"] == "kh"


def test_default_settings_types():
    """Test that default settings have correct types"""
    settings = get_default_user_settings()
    
    assert isinstance(settings["low_cost_only"], bool)
    assert isinstance(settings["phnom_penh_only"], bool)
    assert isinstance(settings["language"], str)


def test_default_settings_immutable():
    """Test that default settings function returns new dict each time"""
    settings1 = get_default_user_settings()
    settings2 = get_default_user_settings()
    
    # Should be equal but not the same object
    assert settings1 == settings2
    assert settings1 is not settings2
    
    # Modifying one shouldn't affect the other
    settings1["low_cost_only"] = True
    assert settings2["low_cost_only"] is False
