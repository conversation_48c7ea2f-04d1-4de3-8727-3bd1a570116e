"""
Additional metrics tests for coverage
"""

import sys
from pathlib import Path
from unittest.mock import patch, mock_open
import time

# Add project root to path
sys.path.append(str(Path(__file__).parents[2]))

from src.infra.metrics import count_calls, track_latency, log_event


def test_count_calls_decorator():
    """Test count_calls decorator functionality"""
    
    @count_calls("test_function")
    def test_func():
        return "test_result"
    
    # Call the function multiple times
    result1 = test_func()
    result2 = test_func()
    
    assert result1 == "test_result"
    assert result2 == "test_result"


def test_track_latency_decorator():
    """Test track_latency decorator functionality"""
    
    @track_latency("test_latency")
    def test_func():
        time.sleep(0.01)  # Small delay
        return "test_result"
    
    result = test_func()
    assert result == "test_result"


def test_track_latency_with_exception():
    """Test track_latency decorator when function raises exception"""
    
    @track_latency("test_latency_error")
    def test_func():
        raise ValueError("Test error")
    
    try:
        test_func()
        assert False, "Should have raised ValueError"
    except ValueError as e:
        assert str(e) == "Test error"


def test_log_event_with_metadata():
    """Test log_event with metadata"""
    with patch('builtins.open', mock_open()) as mock_file:
        log_event("test_event", {"key": "value", "count": 5})

        # Should have opened file for writing
        mock_file.assert_called()
        # Should have written JSON data
        handle = mock_file()
        handle.write.assert_called()
        written_data = handle.write.call_args[0][0]
        assert "test_event" in written_data


def test_log_event_without_metadata():
    """Test log_event without metadata"""
    with patch('builtins.open', mock_open()) as mock_file:
        log_event("simple_event")

        # Should have opened file for writing
        mock_file.assert_called()
        # Should have written JSON data
        handle = mock_file()
        handle.write.assert_called()
        written_data = handle.write.call_args[0][0]
        assert "simple_event" in written_data


def test_count_calls_with_async_function():
    """Test count_calls decorator with async function"""
    import asyncio
    
    @count_calls("async_test")
    async def async_test_func():
        await asyncio.sleep(0.001)
        return "async_result"
    
    async def run_test():
        result = await async_test_func()
        assert result == "async_result"
    
    asyncio.run(run_test())


def test_track_latency_with_async_function():
    """Test track_latency decorator with async function"""
    import asyncio
    
    @track_latency("async_latency")
    async def async_test_func():
        await asyncio.sleep(0.001)
        return "async_result"
    
    async def run_test():
        result = await async_test_func()
        assert result == "async_result"
    
    asyncio.run(run_test())
