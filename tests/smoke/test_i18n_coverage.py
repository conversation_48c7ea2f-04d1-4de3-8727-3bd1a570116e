"""
Additional i18n tests for coverage
"""

import sys
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parents[2]))

from src.bot.i18n import t, get_lang, set_lang


def test_get_lang_with_empty_user_data():
    """Test get_lang with empty user data"""
    result = get_lang({})
    assert result == "kh"  # Default language


def test_get_lang_with_none_user_data():
    """Test get_lang with None user data"""
    # get_lang expects a dict, not None, so this should be handled gracefully
    try:
        result = get_lang(None)
        assert result == "kh"  # Default language
    except AttributeError:
        # This is expected behavior - get_lang expects a dict
        pass


def test_set_lang_returns_new_language():
    """Test that set_lang returns the new language"""
    user_data = {}
    result = set_lang(user_data, "en")
    assert result == "en"
    assert user_data["lang"] == "en"  # set_lang uses "lang" key, not "language"


def test_set_lang_overwrites_existing():
    """Test that set_lang overwrites existing language"""
    user_data = {"lang": "en"}  # Use "lang" key, not "language"
    result = set_lang(user_data, "kh")
    assert result == "kh"
    assert user_data["lang"] == "kh"


def test_t_with_invalid_key():
    """Test translation with invalid key"""
    result = t("nonexistent_key", "en")
    assert result == "nonexistent_key"  # Should return key as fallback


def test_t_with_invalid_language():
    """Test translation with invalid language"""
    # Should fallback to default language
    result = t("start_greeting", "invalid_lang")
    assert len(result) > 0  # Should return some translation


def test_all_translation_keys_exist_in_both_languages():
    """Test that all keys exist in both language files"""
    # Test some core keys that should exist
    core_keys = [
        "start_greeting",
        "help_title", 
        "settings_title",
        "hybrid_score_explanation",
        "button_back_to_list",
        "button_why_recommended"
    ]
    
    for key in core_keys:
        en_result = t(key, "en")
        kh_result = t(key, "kh")
        
        # Both should return non-empty strings
        assert len(en_result) > 0, f"English translation missing for {key}"
        assert len(kh_result) > 0, f"Khmer translation missing for {key}"
        
        # Should not return the key itself (indicating missing translation)
        assert en_result != key, f"English translation not found for {key}"
        assert kh_result != key, f"Khmer translation not found for {key}"
