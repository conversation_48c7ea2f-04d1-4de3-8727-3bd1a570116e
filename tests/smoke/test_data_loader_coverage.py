"""
Additional data loader tests for coverage
"""

import sys
from pathlib import Path
from unittest.mock import patch, mock_open
import json

# Add project root to path
sys.path.append(str(Path(__file__).parents[2]))

from src.core.data_loader import load_raw


def test_load_raw_with_empty_directory():
    """Test load_raw when no JSON files are found"""
    with patch('pathlib.Path.glob') as mock_glob:
        mock_glob.return_value = []  # No files found
        
        result = load_raw()
        assert result == []


def test_load_raw_with_invalid_json():
    """Test load_raw with invalid JSON file"""
    mock_file_path = Path("test.json")
    
    with patch('pathlib.Path.glob') as mock_glob, \
         patch('builtins.open', mock_open(read_data='invalid json')) as mock_file:
        
        mock_glob.return_value = [mock_file_path]
        
        # Should handle JSON decode error gracefully
        result = load_raw()
        assert result == []


def test_load_raw_handles_errors_gracefully():
    """Test that load_raw handles various error conditions gracefully"""
    # Test with no files found
    with patch('pathlib.Path.glob') as mock_glob:
        mock_glob.return_value = []
        result = load_raw()
        assert result == []

    # Test with file read error
    mock_file_path = Path("test.json")
    with patch('pathlib.Path.glob') as mock_glob, \
         patch('builtins.open', side_effect=IOError("File not found")):

        mock_glob.return_value = [mock_file_path]
        result = load_raw()
        assert result == []
