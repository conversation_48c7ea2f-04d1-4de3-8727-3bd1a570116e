"""
Smoke tests for i18n functionality
"""

import sys
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parents[2]))

from src.bot.i18n import t


def test_hybrid_score_copy():
    """Test that hybrid score explanation contains expected text"""
    for lang in ("en", "kh"):
        explanation = t("hybrid_score_explanation", lang)
        assert "Hybrid" in explanation or "បញ្ចូលគ្នា" in explanation


def test_button_translations_exist():
    """Test that button translations exist in both languages"""
    for lang in ("en", "kh"):
        back_button = t("button_back_to_list", lang)
        why_button = t("button_why_recommended", lang)
        
        assert len(back_button) > 0
        assert len(why_button) > 0


def test_score_breakdown_formatting():
    """Test that score breakdown can be formatted with sample data"""
    for lang in ("en", "kh"):
        breakdown = t("score_breakdown", lang)
        
        # Should contain format placeholders
        assert "{mcda_score" in breakdown
        assert "{ml_score" in breakdown
        assert "{hybrid_score" in breakdown
        
        # Should be able to format with sample data
        formatted = breakdown.format(
            mcda_score=0.85,
            ml_score=0.72,
            hybrid_score=0.81
        )
        assert "0.85" in formatted
        assert "0.72" in formatted
        assert "0.81" in formatted


def test_filter_translations():
    """Test that filter-related translations exist"""
    for lang in ("en", "kh"):
        low_cost = t("filter_low_cost", lang)
        phnom_penh = t("filter_phnom_penh", lang)
        enabled = t("filter_enabled", lang)
        disabled = t("filter_disabled", lang)
        
        assert len(low_cost) > 0
        assert len(phnom_penh) > 0
        assert len(enabled) > 0
        assert len(disabled) > 0


def test_explanation_translations():
    """Test that explanation translations exist"""
    for lang in ("en", "kh"):
        mcda_explanation = t("mcda_explanation", lang)
        ml_explanation = t("ml_explanation", lang)
        
        assert len(mcda_explanation) > 0
        assert len(ml_explanation) > 0
        
        # Should mention key concepts
        if lang == "en":
            assert "MCDA" in mcda_explanation or "Multi-Criteria" in mcda_explanation
            assert "ML" in ml_explanation or "Machine Learning" in ml_explanation
