"""
MCDA Specific Coverage Tests
Targeted tests to improve MCDA module coverage
"""

import sys
from pathlib import Path
from unittest.mock import patch, mock_open
import numpy as np

# Add project root to path
sys.path.append(str(Path(__file__).parents[1]))

from src.core.mcda import (
    score_vectorized, score, load_weights, generate_reason,
    _calculate_location_scores_vectorized,
    _calculate_budget_scores_vectorized,
    _calculate_field_scores_vectorized
)


def test_load_weights_with_file_error():
    """Test load_weights when file exists but can't be read"""
    with patch("pathlib.Path.exists", return_value=True):
        with patch("builtins.open", side_effect=IOError("Permission denied")):
            weights = load_weights()
            assert isinstance(weights, dict)
            assert "question_weights" in weights


def test_load_weights_with_yaml_error():
    """Test load_weights with YAML parsing error"""
    with patch("pathlib.Path.exists", return_value=True):
        with patch("builtins.open", mock_open(read_data="invalid: yaml: [content")):
            with patch("yaml.safe_load", side_effect=Exception("YAML error")):
                weights = load_weights()
                assert isinstance(weights, dict)


def test_score_vectorized_with_empty_weights():
    """Test score_vectorized with empty weights"""
    with patch("src.core.mcda.load_weights", return_value={"question_weights": {}}):
        user_answers = {"location_preference": "pp"}
        programmes = [{"major_id": "test", "city": "ភ្នំពេញ"}]
        
        scores = score_vectorized(user_answers, programmes)
        assert isinstance(scores, list)
        assert len(scores) == 1


def test_vectorized_scoring_functions_empty():
    """Test vectorized scoring functions with empty inputs"""
    user_answers = {}
    weights = {"question_weights": {}}
    
    # Test with empty numpy arrays
    empty_array = np.array([])
    
    # Location scoring
    scores = _calculate_location_scores_vectorized(
        user_answers, weights, empty_array, empty_array, empty_array
    )
    assert len(scores) == 0
    
    # Budget scoring
    scores = _calculate_budget_scores_vectorized(
        user_answers, weights, empty_array, empty_array, empty_array
    )
    assert len(scores) == 0
    
    # Field scoring
    scores = _calculate_field_scores_vectorized(
        user_answers, weights, empty_array, empty_array, empty_array
    )
    assert len(scores) == 0


def test_score_vectorized_with_missing_user_data():
    """Test score_vectorized with missing user answer fields"""
    user_answers = {}  # Empty user answers
    programmes = [
        {
            "major_id": "test1",
            "city": "ភ្នំពេញ",
            "field_tag": "STEM",
            "tuition_bracket": "Low",
            "is_phnom_penh": True,
            "is_stem": True,
            "is_low_cost": True
        }
    ]
    
    scores = score_vectorized(user_answers, programmes)
    assert isinstance(scores, list)
    assert len(scores) == 1
    assert 0.0 <= scores[0] <= 1.0


def test_score_vectorized_with_missing_programme_data():
    """Test score_vectorized with programmes missing fields"""
    user_answers = {
        "location_preference": "pp",
        "budget_range": "low",
        "field_of_interest": "stem"
    }
    programmes = [
        {"major_id": "test1"},  # Missing most fields
        {"major_id": "test2", "city": "unknown"}  # Partial data
    ]
    
    scores = score_vectorized(user_answers, programmes)
    assert isinstance(scores, list)
    assert len(scores) == 2


def test_generate_reason_with_minimal_data():
    """Test generate_reason with minimal data"""
    user_answers = {}
    programme = {"major_id": "test"}
    
    reason = generate_reason(user_answers, programme, 0.5)
    assert isinstance(reason, str)
    assert len(reason) > 0


def test_score_with_edge_case_programmes():
    """Test score function with edge case programmes"""
    user_answers = {"location_preference": "pp"}
    
    # Test with programme that has None values
    programme = {
        "major_id": "test",
        "city": None,
        "field_tag": None,
        "tuition_bracket": None
    }
    
    result = score(user_answers, programme)
    assert isinstance(result, (int, float))
    assert 0.0 <= result <= 1.0


def test_score_vectorized_normalization_edge_cases():
    """Test score_vectorized normalization with edge cases"""
    # Mock weights that might cause normalization issues
    mock_weights = {
        "question_weights": {},  # Empty weights
        "scholarship_bonus": 0,
        "employment_multiplier": 1.0
    }
    
    with patch("src.core.mcda.load_weights", return_value=mock_weights):
        user_answers = {"location_preference": "pp"}
        programmes = [{"major_id": "test", "city": "ភ្នំពេញ"}]
        
        scores = score_vectorized(user_answers, programmes)
        assert isinstance(scores, list)
        assert len(scores) == 1
        assert 0.0 <= scores[0] <= 1.0
