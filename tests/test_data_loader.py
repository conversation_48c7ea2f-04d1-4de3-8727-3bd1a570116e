"""
Data Loader Tests
Unit tests for data loading functionality
"""

import pytest
import sys
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parents[1]))

from src.core.data_loader import load_raw, validate_program_data


class TestDataLoader:
    """Test cases for data loader module"""
    
    def test_load_raw_returns_list(self):
        """
        Test that load_raw() returns a list
        """
        result = load_raw()
        assert isinstance(result, list)
        # Skip assertion if no data files are present
        if len(result) == 0:
            pytest.skip("No data files found in data/raw directory")
    
    def test_load_raw_program_count(self):
        """
        Test that load_raw() returns expected number of programs
        """
        result = load_raw()
        # Skip if no data files are present
        if len(result) == 0:
            pytest.skip("No data files found in data/raw directory")
        # Should have reasonable number of programs (> 100)
        assert len(result) > 100
    
    def test_validate_program_data_valid(self):
        """
        Test program validation with valid data
        """
        valid_program = {
            'university_id': 'test_uni',
            'university_name_kh': 'សាកលវិទ្យាល័យសាកល្បង',
            'university_name_en': 'Test University',
            'major_id': 'test_major',
            'major_name_kh': 'ជំនាញសាកល្បង',
            'major_name_en': 'Test Major',
            'city': 'ភ្នំពេញ'
        }
        assert validate_program_data(valid_program) == True
    
    def test_validate_program_data_invalid(self):
        """
        Test program validation with invalid data
        """
        invalid_program = {
            'university_id': 'test_uni',
            # Missing required fields
        }
        assert validate_program_data(invalid_program) == False


if __name__ == '__main__':
    pytest.main([__file__])
