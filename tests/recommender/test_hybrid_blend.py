"""
Hybrid Recommender Blend Tests
Tests for 70% MCDA / 30% ML blend strategy
"""

import pytest
import numpy as np
import sys
from pathlib import Path
from unittest.mock import patch, MagicMock

# Add project root to path
sys.path.append(str(Path(__file__).parents[2]))

from src.core.hybrid_recommender import (
    get_recommendations,
    _get_ml_scores,
    MCDA_WEIGHT,
    ML_WEIGHT
)


@pytest.fixture
def sample_user_answers():
    """Sample user answers for testing"""
    return {
        'location_preference': 'pp',
        'budget_range': 'low',
        'field_of_interest': 'stem',
        'career_goal': 'tech',
        'scholarship_need': 'yes'
    }


@pytest.fixture
def sample_programmes():
    """Sample programme data for testing"""
    return [
        {
            'major_id': 'cs-001',
            'major_name_kh': 'វិទ្យាសាស្ត្រកុំព្យូទ័រ',
            'major_name_en': 'Computer Science',
            'university_name_kh': 'សាកលវិទ្យាល័យភូមិន្ទ',
            'city': 'ភ្នំពេញ',
            'tuition_fees_usd': 400,
            'employment_rate': 90,
            'scholarship_availability': True,
            'is_phnom_penh': True,
            'is_siem_reap': False,
            'is_battambang': False,
            'is_stem': True,
            'is_business': False,
            'is_arts': False,
            'is_low_cost': True,
            'is_medium_cost': False,
            'is_high_cost': False
        },
        {
            'major_id': 'bus-001',
            'major_name_kh': 'គ្រប់គ្រងអាជីវកម្ម',
            'major_name_en': 'Business Management',
            'university_name_kh': 'សាកលវិទ្យាល័យពាណិជ្ជសាស្ត្រ',
            'city': 'សៀមរាប',
            'tuition_fees_usd': 800,
            'employment_rate': 75,
            'scholarship_availability': False,
            'is_phnom_penh': False,
            'is_siem_reap': True,
            'is_battambang': False,
            'is_stem': False,
            'is_business': True,
            'is_arts': False,
            'is_low_cost': False,
            'is_medium_cost': True,
            'is_high_cost': False
        },
        {
            'major_id': 'eng-001',
            'major_name_kh': 'វិស្វកម្មស៊ីវិល',
            'major_name_en': 'Civil Engineering',
            'university_name_kh': 'វិទ្យាស្ថានបច្ចេកវិទ្យា',
            'city': 'ភ្នំពេញ',
            'tuition_fees_usd': 600,
            'employment_rate': 85,
            'scholarship_availability': True,
            'is_phnom_penh': True,
            'is_siem_reap': False,
            'is_battambang': False,
            'is_stem': True,
            'is_business': False,
            'is_arts': False,
            'is_low_cost': False,
            'is_medium_cost': True,
            'is_high_cost': False
        }
    ]


class TestBlendWeights:
    """Test blend weight constants"""
    
    def test_blend_weights_sum_to_one(self):
        """Test that MCDA and ML weights sum to 1.0"""
        assert abs(MCDA_WEIGHT + ML_WEIGHT - 1.0) < 1e-10
    
    def test_mcda_weight_dominance(self):
        """Test that MCDA weight is dominant (70%)"""
        assert MCDA_WEIGHT == 0.7
        assert ML_WEIGHT == 0.3
        assert MCDA_WEIGHT > ML_WEIGHT


class TestMLScores:
    """Test ML score generation"""
    
    @patch('src.core.hybrid_recommender.MLReranker')
    @patch('src.core.hybrid_recommender.Path')
    def test_ml_scores_with_model(self, mock_path, mock_ml_reranker, sample_user_answers, sample_programmes):
        """Test ML score generation with working model"""
        # Mock model exists
        mock_path.return_value.exists.return_value = True
        
        # Mock ML reranker
        mock_reranker_instance = MagicMock()
        mock_reranker_instance.model = MagicMock()
        mock_reranker_instance.scaler = MagicMock()
        mock_reranker_instance.scaler.transform.return_value = [[0.5, 0.3, 0.8]]
        mock_reranker_instance.model.predict.return_value = [0.75]
        mock_ml_reranker.return_value = mock_reranker_instance
        
        scores = _get_ml_scores(sample_user_answers, sample_programmes)
        
        assert isinstance(scores, np.ndarray)
        assert len(scores) == len(sample_programmes)
        assert all(0.0 <= score <= 1.0 for score in scores)
    
    @patch('src.core.hybrid_recommender.Path')
    def test_ml_scores_fallback(self, mock_path, sample_user_answers, sample_programmes):
        """Test ML score fallback when model not available"""
        # Mock that no models exist
        mock_path.return_value.exists.return_value = False

        scores = _get_ml_scores(sample_user_answers, sample_programmes)

        assert isinstance(scores, np.ndarray)
        assert len(scores) == len(sample_programmes)
        # Should return neutral scores (0.5) when model fails
        assert all(score == 0.5 for score in scores)
    
    @patch.dict('os.environ', {'ML_VERSION': '2'})
    def test_ml_version_env_var(self, sample_user_answers, sample_programmes):
        """Test ML version environment variable handling"""
        scores = _get_ml_scores(sample_user_answers, sample_programmes)
        
        # Should handle environment variable gracefully
        assert isinstance(scores, np.ndarray)
        assert len(scores) == len(sample_programmes)


class TestHybridRecommendations:
    """Test hybrid recommendation generation"""
    
    def test_get_recommendations_basic(self, sample_user_answers, sample_programmes):
        """Test basic recommendation generation"""
        recommendations = get_recommendations(
            sample_user_answers, 
            programmes=sample_programmes, 
            top_k=3
        )
        
        assert isinstance(recommendations, list)
        assert len(recommendations) <= 3
        assert len(recommendations) == len(sample_programmes)  # All 3 programmes
        
        # Check that all required fields are present
        for rec in recommendations:
            assert 'mcda_score' in rec
            assert 'ml_score' in rec
            assert 'hybrid_score' in rec
            assert 'mcda_reason' in rec
            assert 'major_id' in rec
    
    def test_recommendations_sorted_by_hybrid_score(self, sample_user_answers, sample_programmes):
        """Test that recommendations are sorted by hybrid score"""
        recommendations = get_recommendations(
            sample_user_answers,
            programmes=sample_programmes,
            top_k=3
        )
        
        # Should be sorted in descending order by hybrid_score
        hybrid_scores = [rec['hybrid_score'] for rec in recommendations]
        assert hybrid_scores == sorted(hybrid_scores, reverse=True)
    
    def test_hybrid_score_calculation(self, sample_user_answers, sample_programmes):
        """Test that hybrid scores are calculated correctly"""
        recommendations = get_recommendations(
            sample_user_answers,
            programmes=sample_programmes,
            top_k=3
        )
        
        for rec in recommendations:
            expected_hybrid = MCDA_WEIGHT * rec['mcda_score'] + ML_WEIGHT * rec['ml_score']
            assert abs(rec['hybrid_score'] - expected_hybrid) < 1e-10
    
    def test_score_ranges(self, sample_user_answers, sample_programmes):
        """Test that all scores are in valid ranges"""
        recommendations = get_recommendations(
            sample_user_answers,
            programmes=sample_programmes,
            top_k=3
        )
        
        for rec in recommendations:
            assert 0.0 <= rec['mcda_score'] <= 1.0
            assert 0.0 <= rec['ml_score'] <= 1.0
            assert 0.0 <= rec['hybrid_score'] <= 1.0
    
    def test_top_k_limiting(self, sample_user_answers, sample_programmes):
        """Test that top_k parameter limits results correctly"""
        # Test with k=1
        recommendations = get_recommendations(
            sample_user_answers,
            programmes=sample_programmes,
            top_k=1
        )
        assert len(recommendations) == 1
        
        # Test with k=2
        recommendations = get_recommendations(
            sample_user_answers,
            programmes=sample_programmes,
            top_k=2
        )
        assert len(recommendations) == 2
    
    def test_empty_programmes(self, sample_user_answers):
        """Test with empty programmes list"""
        recommendations = get_recommendations(
            sample_user_answers,
            programmes=[],
            top_k=5
        )
        assert recommendations == []
    
    @patch('src.core.hybrid_recommender.load_raw')
    @patch('src.core.hybrid_recommender.add_derived_features')
    def test_data_loading_when_programmes_none(self, mock_add_features, mock_load_raw, sample_user_answers, sample_programmes):
        """Test data loading when programmes parameter is None"""
        mock_load_raw.return_value = sample_programmes
        mock_add_features.return_value = sample_programmes
        
        recommendations = get_recommendations(sample_user_answers, programmes=None, top_k=2)
        
        # Should call data loading functions
        mock_load_raw.assert_called_once()
        mock_add_features.assert_called_once_with(sample_programmes)
        
        assert len(recommendations) <= 2


class TestPreferenceMatching:
    """Test that hybrid scoring reflects user preferences"""
    
    def test_location_preference_impact(self, sample_programmes):
        """Test that location preference affects MCDA component of scoring"""
        # User prefers Phnom Penh with STEM preference
        pp_user = {
            'location_preference': 'pp',
            'budget_range': 'low',
            'field_of_interest': 'stem'
        }

        # User prefers Siem Reap with business preference
        sr_user = {
            'location_preference': 'sr',
            'budget_range': 'medium',
            'field_of_interest': 'business'
        }

        pp_recs = get_recommendations(pp_user, programmes=sample_programmes, top_k=3)
        sr_recs = get_recommendations(sr_user, programmes=sample_programmes, top_k=3)

        # Test that MCDA scores reflect location preferences
        # Find CS programme (in PP) and Business programme (in SR)
        cs_prog_pp = next((rec for rec in pp_recs if rec['major_id'] == 'cs-001'), None)
        cs_prog_sr = next((rec for rec in sr_recs if rec['major_id'] == 'cs-001'), None)

        bus_prog_pp = next((rec for rec in pp_recs if rec['major_id'] == 'bus-001'), None)
        bus_prog_sr = next((rec for rec in sr_recs if rec['major_id'] == 'bus-001'), None)

        # PP user should give higher MCDA score to CS (in PP) than Business (in SR)
        if cs_prog_pp and bus_prog_pp:
            assert cs_prog_pp['mcda_score'] > bus_prog_pp['mcda_score'], \
                f"PP user should prefer CS in PP (MCDA: {cs_prog_pp['mcda_score']:.3f}) over Business in SR (MCDA: {bus_prog_pp['mcda_score']:.3f})"

        # SR user should give higher MCDA score to Business (in SR) than when PP user evaluates it
        if bus_prog_pp and bus_prog_sr:
            # SR user should score business programme higher than PP user does
            assert bus_prog_sr['mcda_score'] >= bus_prog_pp['mcda_score'], \
                f"SR user should score Business programme at least as high as PP user: {bus_prog_sr['mcda_score']:.3f} >= {bus_prog_pp['mcda_score']:.3f}"
    
    def test_field_preference_impact(self, sample_programmes):
        """Test that field preference affects ranking"""
        # STEM-focused user with strong preferences
        stem_user = {
            'location_preference': 'pp',
            'budget_range': 'low',
            'field_of_interest': 'stem',
            'scholarship_need': 'yes'  # CS programme has scholarship
        }

        # Business-focused user with matching preferences
        business_user = {
            'location_preference': 'sr',
            'budget_range': 'medium',
            'field_of_interest': 'business'
        }

        stem_recs = get_recommendations(stem_user, programmes=sample_programmes, top_k=3)
        business_recs = get_recommendations(business_user, programmes=sample_programmes, top_k=3)

        # Debug: Print actual rankings
        print(f"STEM user rankings: {[(rec['major_id'], rec['hybrid_score']) for rec in stem_recs]}")
        print(f"Business user rankings: {[(rec['major_id'], rec['hybrid_score']) for rec in business_recs]}")

        # Check that field preferences have impact
        # Find STEM programmes in STEM user's results
        stem_programmes = [rec for rec in stem_recs if rec.get('is_stem', False)]
        business_programmes = [rec for rec in business_recs if rec.get('is_business', False)]

        # Should have at least some programmes of preferred type
        assert len(stem_programmes) > 0, "STEM user should have STEM programmes in results"
        assert len(business_programmes) > 0, "Business user should have business programmes in results"

        # STEM programmes should score well for STEM user
        if stem_programmes:
            stem_programme_score = stem_programmes[0]['hybrid_score']
            assert stem_programme_score > 0.0, "STEM programme should have positive score for STEM user"


class TestEdgeCases:
    """Test edge cases and error handling"""
    
    def test_invalid_user_answers(self, sample_programmes):
        """Test with invalid user answers"""
        invalid_answers = {
            'location_preference': 'invalid_location',
            'budget_range': None,
            'field_of_interest': 123  # Invalid type
        }
        
        recommendations = get_recommendations(
            invalid_answers,
            programmes=sample_programmes,
            top_k=2
        )
        
        # Should handle gracefully
        assert isinstance(recommendations, list)
        assert len(recommendations) <= 2
    
    def test_programmes_missing_fields(self, sample_user_answers):
        """Test with programmes missing required fields"""
        incomplete_programmes = [
            {
                'major_id': 'test-001',
                'major_name_kh': 'ការសាកល្បង'
                # Missing many fields
            }
        ]
        
        recommendations = get_recommendations(
            sample_user_answers,
            programmes=incomplete_programmes,
            top_k=1
        )
        
        # Should not crash
        assert isinstance(recommendations, list)
        assert len(recommendations) <= 1
        
        if recommendations:
            rec = recommendations[0]
            assert 'mcda_score' in rec
            assert 'ml_score' in rec
            assert 'hybrid_score' in rec
    
    def test_zero_top_k(self, sample_user_answers, sample_programmes):
        """Test with top_k=0"""
        recommendations = get_recommendations(
            sample_user_answers,
            programmes=sample_programmes,
            top_k=0
        )
        assert recommendations == []
