{"timestamp": "2025-06-15T15:29:40.080385+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.15}}
{"timestamp": "2025-06-15T15:29:40.080714+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0}}
{"timestamp": "2025-06-15T15:29:40.080779+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.5}}
{"timestamp": "2025-06-15T15:29:40.085462+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.15}}
{"timestamp": "2025-06-15T15:29:40.085655+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0}}
{"timestamp": "2025-06-15T15:29:40.085722+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.5}}
{"timestamp": "2025-06-15T15:29:40.090535+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.15}}
{"timestamp": "2025-06-15T15:29:40.090689+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0}}
{"timestamp": "2025-06-15T15:29:40.090748+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.5}}
{"timestamp": "2025-06-15T15:29:40.095288+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.15}}
{"timestamp": "2025-06-15T15:29:40.095421+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0}}
{"timestamp": "2025-06-15T15:29:40.095472+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.5}}
{"timestamp": "2025-06-15T15:29:40.101763+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.15}}
{"timestamp": "2025-06-15T15:29:40.137509+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0}}
{"timestamp": "2025-06-15T15:29:40.137657+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.5}}
{"timestamp": "2025-06-15T15:29:40.140113+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.15}}
{"timestamp": "2025-06-15T15:29:40.140228+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0}}
{"timestamp": "2025-06-15T15:29:40.140280+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.5}}
{"timestamp": "2025-06-15T15:29:40.371675+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.004125}}
{"timestamp": "2025-06-15T15:29:40.371908+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0}}
{"timestamp": "2025-06-15T15:29:40.371971+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.01375}}
{"timestamp": "2025-06-15T15:29:40.413243+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.15}}
{"timestamp": "2025-06-15T15:29:40.413728+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0}}
{"timestamp": "2025-06-15T15:29:40.415925+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.5}}
{"timestamp": "2025-06-15T15:29:40.624947+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.15}}
{"timestamp": "2025-06-15T15:29:40.625277+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0}}
{"timestamp": "2025-06-15T15:29:40.625356+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.5}}
{"timestamp": "2025-06-15T15:29:40.930482+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.004125}}
{"timestamp": "2025-06-15T15:29:40.930681+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0}}
{"timestamp": "2025-06-15T15:29:40.930737+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.01375}}
{"timestamp": "2025-06-15T15:29:40.934083+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.15}}
{"timestamp": "2025-06-15T15:29:40.934212+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0}}
{"timestamp": "2025-06-15T15:29:40.934264+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.5}}
{"timestamp": "2025-06-15T15:33:19.570558+00:00", "event": "call_count", "data": {"event_name": "start", "function": "start_assessment"}}
{"timestamp": "2025-06-15T15:34:10.013149+00:00", "event": "call_count", "data": {"event_name": "start", "function": "start_assessment"}}
