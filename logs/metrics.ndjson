{"timestamp": "2025-06-15T16:29:19.644592+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.15}}
{"timestamp": "2025-06-15T16:29:19.644882+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0}}
{"timestamp": "2025-06-15T16:29:19.645230+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.5}}
{"timestamp": "2025-06-15T16:29:19.649509+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.15}}
{"timestamp": "2025-06-15T16:29:19.649616+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0}}
{"timestamp": "2025-06-15T16:29:19.649670+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.5}}
{"timestamp": "2025-06-15T16:29:19.653665+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.15}}
{"timestamp": "2025-06-15T16:29:19.653761+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0}}
{"timestamp": "2025-06-15T16:29:19.653821+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.5}}
{"timestamp": "2025-06-15T16:29:19.657498+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.15}}
{"timestamp": "2025-06-15T16:29:19.657560+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0}}
{"timestamp": "2025-06-15T16:29:19.657611+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.5}}
{"timestamp": "2025-06-15T16:29:19.662569+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.15}}
{"timestamp": "2025-06-15T16:29:19.662672+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0}}
{"timestamp": "2025-06-15T16:29:19.662724+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.5}}
{"timestamp": "2025-06-15T16:29:19.664592+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.15}}
{"timestamp": "2025-06-15T16:29:19.664655+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0}}
{"timestamp": "2025-06-15T16:29:19.664702+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.5}}
{"timestamp": "2025-06-15T16:29:19.694814+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.15}}
{"timestamp": "2025-06-15T16:29:19.694925+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0}}
{"timestamp": "2025-06-15T16:29:19.694976+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.5}}
{"timestamp": "2025-06-15T16:29:19.757372+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.15}}
{"timestamp": "2025-06-15T16:29:19.757507+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0}}
{"timestamp": "2025-06-15T16:29:19.757559+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.5}}
{"timestamp": "2025-06-15T16:29:19.907961+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.15}}
{"timestamp": "2025-06-15T16:29:19.908092+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0}}
{"timestamp": "2025-06-15T16:29:19.908142+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.5}}
{"timestamp": "2025-06-15T16:29:20.074815+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.004125}}
{"timestamp": "2025-06-15T16:29:20.075032+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0}}
{"timestamp": "2025-06-15T16:29:20.075347+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.01375}}
{"timestamp": "2025-06-15T16:29:20.077077+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.15}}
{"timestamp": "2025-06-15T16:29:20.077145+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0}}
{"timestamp": "2025-06-15T16:29:20.077195+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.5}}
{"timestamp": "2025-06-15T23:30:05.841813+00:00", "event": "call_count", "data": {"event_name": "start", "function": "start_assessment"}}
{"timestamp": "2025-06-15T23:30:49.956723+00:00", "event": "latency", "data": {"event_name": "recommendation_latency", "latency_ms": 16232.140958003583, "function": "show_recommendations"}}
{"timestamp": "2025-06-15T23:47:14.747677+00:00", "event": "call_count", "data": {"event_name": "start", "function": "start_assessment"}}
{"timestamp": "2025-06-15T23:47:51.691924+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.09813835714285714}}
{"timestamp": "2025-06-15T23:47:51.692162+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.018039999999999997}}
{"timestamp": "2025-06-15T23:47:51.692493+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.28503452380952377}}
{"timestamp": "2025-06-15T23:47:55.060472+00:00", "event": "recommendation_sent", "data": {"count": 5}}
{"timestamp": "2025-06-15T23:47:55.509482+00:00", "event": "latency", "data": {"event_name": "recommendation_latency", "latency_ms": 18878.0225829978, "function": "show_recommendations"}}
{"timestamp": "2025-06-15T23:48:25.461685+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.009300000000000003}}
{"timestamp": "2025-06-15T23:48:25.461898+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0}}
{"timestamp": "2025-06-15T23:48:25.462176+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.03100000000000001}}
{"timestamp": "2025-06-15T23:49:09.448359+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.021200000000000004}}
{"timestamp": "2025-06-15T23:49:09.448604+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.017}}
{"timestamp": "2025-06-15T23:49:09.448912+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.03100000000000001}}
